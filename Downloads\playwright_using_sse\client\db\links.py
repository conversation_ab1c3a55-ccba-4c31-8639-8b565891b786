import uuid
import psycopg2
import os
from dotenv import load_dotenv

load_dotenv()

DB = {
    "dbname": "QATOOL",
    "user": "postgres",
    "password": os.getenv("DB_PASSWORD"),
    "host": "localhost",
    "port": "5432"
}

def get_id_by_column(table, column, value):
    id_column_map = {
        "Projects": "project_id",
        "ProjectFiles": "file_id",
        "ProjectLinks": "link_id",
        "TestCases": "testcase_id",
        "TestSteps": "step_id",
        "TestRuns": "run_id",
        "AutomationScripts": "script_id",
        "TestRunScripts": "id",
        "TestResults": "result_id"
    }

    id_column = id_column_map.get(table)
    if not id_column:
        raise ValueError(f"❌ No ID column mapping defined for table '{table}'")

    with psycopg2.connect(**DB) as conn:
        with conn.cursor() as cur:
            cur.execute(f"SELECT {id_column} FROM {table} WHERE {column} = %s", (value,))
            result = cur.fetchone()
            if result:
                return result[0]
            raise ValueError(f"❌ No row found in {table}.{column} = {value}")

def insert_project_link(project_name, filename, url, page_title=None):
    project_id = get_id_by_column("Projects", "project_name", project_name)
    file_id = get_id_by_column("ProjectFiles", "filename", filename)

    link_id = str(uuid.uuid4())

    with psycopg2.connect(**DB) as conn:
        with conn.cursor() as cur:
            cur.execute("""
                INSERT INTO ProjectLinks (link_id, project_id, file_id, url, page_title)
                VALUES (%s, %s, %s, %s, %s)
            """, (link_id, project_id, file_id, url, page_title))
        conn.commit()
    print(f"✅ Link inserted for project '{project_name}'")

if __name__ == "__main__":
    insert_project_link(
        "Form Testing",
        "form_spec_google_doc",
        "https://form2-4y5z.onrender.com/",
        page_title="Form Testing App"
    )
