with st.spinner("Refining your prompt via n8n..."):
                response = requests.post(N8N_WEBHOOK_URL, json={"initial_prompt": user_input})
                
                if response.status_code == 200:
                    refined_prompt = str(response.json().get("output", ""))
                    
                    st.session_state.chat_history.append(("You", user_input))
                    st.session_state.chat_history.append(("Assistant", f"🧠 Refined Prompt:\n\n```{refined_prompt}```"))
                    # Run refined prompt via MCP agent
                    print(type(refined_prompt))
                    final_response = await st.session_state.agent.run(refined_prompt)
                    st.session_state.chat_history.append(("Assistant", final_response))
                else:
                    st.session_state.chat_history.append(("Error", f"❌ n8n returned status {response.status_code}: {response.text}"))
        except Exception as e:
            st.session_state.chat_history.append(("Error", str(e)))

    asyncio.run(handle_and_run())
    st.rerun()

    npx @playwright/mcp@latest --port 8931

    https://docs.google.com/document/d/1FJGQMGvB_G42gaY5jDFZjMK41mx-65uXWbgTaNrG-hI/edit?tab=t.0

  


