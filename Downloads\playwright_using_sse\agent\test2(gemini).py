import streamlit as st
import asyncio
from dotenv import load_dotenv
import os
import time
import streamlit.components.v1 as components

from langchain_google_genai import ChatGoogleGenerativeAI
from mcp_use import MCPAgent, MCPClient

# Load environment variables
load_dotenv()
os.environ["GOOGLE_API_KEY"] = os.getenv("GOOGLE_API_KEY")
config_file = "mcp-playwright.json"

# Initialize session state
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []
if "report_displayed" not in st.session_state:
    st.session_state.report_displayed = False

system_prompt = (
    "You are an intelligent QA testing assistant equipped with web automation capabilities using Playwright via MCP. "
    "When a user provides a URL and specifies a field to validate (e.g., 'first name'), follow these steps: \n"
    "1. Navigate to the given URL. \n"
    "2. Locate the specified field and perform full validation tests (e.g., empty input, long input, numbers, special characters, valid input). \n"
    "3. Submit the form after each test and observe any validation errors surrounding the field, success messages, or UI changes. \n"
    "4. Log each validation outcome clearly, including input tested, expected result, actual result, and any error messages. \n"
    "5. After completing tests, generate Playwright code used for testing and display it. \n"
    "6. Then create an Playwright report page in the following format: \n"
    "   - Group results into Test1, Test2, Test3... each test block should have: \n"
    "     * Test Title (e.g., 'Empty Input Validation')\n"
    "     * Status (PASSED / FAILED)\n"
    "     * Observations or Error Message (if any)\n"
    "     * Screenshot (if applicable)\n"
    "     * Provide the code used for testing\n"
    "7. Use a clean, minimal layout similar to the provided sample, with large headings, separate test blocks, and consistent styling.\n"
    "8. Display the report in the chat interface after generating it.\n"
    "9. **Submit the form after each test input and observe any validation or error messages specific to the field.**\n"
    "Use the tools available via MCP and proceed step-by-step. Ensure report quality and structured formatting before displaying."
)

if "agent" not in st.session_state:
    client = MCPClient.from_config_file(config_file)
    # Use Google Gemini Pro model
    llm = ChatGoogleGenerativeAI(
        model="gemini-1.5-pro",
        google_api_key=os.getenv("GOOGLE_API_KEY"),
        temperature=0.1
    )
    st.session_state.agent = MCPAgent(
        llm=llm,
        client=client,
        max_steps=30,
        memory_enabled=True,
        system_prompt=system_prompt
    )

# Title
st.title("🤖 Interactive MCP Chat")

# Input field
user_input = st.text_input("You:", key="user_input", label_visibility="visible")

# Function to handle user input
async def handle_input():
    try:
        response = await st.session_state.agent.run(user_input)
        st.session_state.chat_history.append(("You", f"{user_input}"))
        st.session_state.chat_history.append(("Assistant", response))

        # === NEW: After test, directly show report ===
        with st.spinner('Loading Playwright report...'):
            time.sleep(2)  # Optional, simulate waiting if needed

            report_path = os.path.abspath("playwright-report/index.html")
            if os.path.exists(report_path):
                with open(report_path, "r", encoding="utf-8") as f:
                    html_content = f.read()

                st.session_state.report_displayed = True

                st.success("Playwright Test Report Loaded ✅")
                components.html(html_content, height=800, scrolling=True)
            else:
                st.error("Playwright report not found! Make sure the test generated a report.")

    except Exception as e:
        st.session_state.chat_history.append(("Error", str(e)))

# Send message button
if st.button("Send") and user_input:
    asyncio.run(handle_input())
    st.rerun()  # Clears input after submission

# Display chat history
for role, message in st.session_state.chat_history:
    if role == "You":
        st.markdown(f"🧑 **{role}:** {message}")
    elif role == "Assistant":
        st.markdown(f"🤖 **{role}:** {message}")
    else:
        st.error(f"{role}: {message}")

# If report was already generated, show again
if st.session_state.get("report_displayed", False):
    report_path = os.path.abspath("playwright-report/index.html")
    if os.path.exists(report_path):
        with open(report_path, "r", encoding="utf-8") as f:
            html_content = f.read()
        components.html(html_content, height=800, scrolling=True)

# Clear conversation history
if st.button("Clear History"):
    st.session_state.agent.clear_conversation_history()
    st.session_state.chat_history = []
    st.session_state.report_displayed = False
    st.rerun()