import subprocess
import platform
import threading
import time
import streamlit as st

# Global variables to track output and process
process = None
stdout_log = ""
stderr_log = ""

def run_allscripts():
    global process, stdout_log, stderr_log

    test_directory = "tests"
    is_windows = platform.system() == "Windows"

    command = [
        "npx",
        "playwright",
        "test",
        test_directory
    ]

    try:
        process = subprocess.Popen(
            command,
            cwd=".",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            shell=is_windows
        )

        stdout, stderr = process.communicate()
        stdout_log = stdout
        stderr_log = stderr

        print("STDOUT:\n", stdout)
        print("STDERR:\n", stderr)

    except Exception as e:
        stderr_log = str(e)
        print("Exception occurred:", e)

    finally:
        # Force terminate the process if still running
        if process:
            try:
                if process.poll() is None:  # Check if process is still running
                    process.kill()
            except Exception as e:
                print(f"Error killing process: {e}")


def run_tests_in_thread():
    thread = threading.Thread(target=run_allscripts)
    thread.daemon = True  # Make thread daemon so it doesn't block app shutdown
    thread.start()
    return thread


def force_cleanup():
    """Force kill any remaining processes and release resources"""
    global process
    
    if process and process.poll() is None:
        try:
            process.kill()
            print("Force terminated process")
        except Exception as e:
            print(f"Error killing process: {e}")
    
    # Force Python to release resources
    import gc
    gc.collect()

