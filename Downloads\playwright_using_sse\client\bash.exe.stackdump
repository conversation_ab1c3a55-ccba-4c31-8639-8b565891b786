Stack trace:
Frame         Function      Args
0007FFFF4200  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF3100) msys-2.0.dll+0x1FE8E
0007FFFF4200  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF44D8) msys-2.0.dll+0x67F9
0007FFFF4200  000210046832 (000210286019, 0007FFFF40B8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF4200  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF4200  000210068E24 (0007FFFF4210, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF44E0  00021006A225 (0007FFFF4210, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA944F0000 ntdll.dll
7FFA937A0000 KERNEL32.DLL
7FFA91B20000 KERNELBASE.dll
7FFA942F0000 USER32.dll
7FFA91AF0000 win32u.dll
7FFA93C50000 GDI32.dll
7FFA91660000 gdi32full.dll
7FFA91F00000 msvcp_win.dll
7FFA91FA0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFA93400000 advapi32.dll
7FFA92BE0000 msvcrt.dll
7FFA934C0000 sechost.dll
7FFA918D0000 bcrypt.dll
7FFA93D00000 RPCRT4.dll
7FFA90D10000 CRYPTBASE.DLL
7FFA91900000 bcryptPrimitives.dll
7FFA93AD0000 IMM32.DLL
