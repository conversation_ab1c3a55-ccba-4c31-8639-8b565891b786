import { test, expect } from '@playwright/test';

test.describe('EMLabs Form Submission', () => {
  const URL = 'https://emlabsform.onrender.com/';

  test('Optional fields blank, mandatory fields filled, successful submission', async ({ page }) => {
    // 1. Navigate to the form page
    try {
      await page.goto(URL, { waitUntil: 'load', timeout: 10_000 });
    } catch {
      throw new Error('Form page failed to load');
    }
    await expect(page).toHaveURL(URL, { timeout: 10_000 });

    // 2. Verify "First Name" (optional)
    const firstName = page.getByLabel('First Name');
    if (!(await firstName.count())) {
      throw new Error('First Name field missing');
    }
    // leave blank

    // 3. Verify "Last Name" (optional)
    const lastName = page.getByLabel('Last Name');
    if (!(await lastName.count())) {
      throw new Error('Last Name field missing');
    }
    // leave blank

    // 4. Verify and fill "Phone Number" (mandatory)
    const phone = page.getByLabel('Phone Number');
    if (!(await phone.count())) {
      throw new Error('Phone Number field missing');
    }
    await phone.fill('123456789');

    // 5. Verify and fill "Email" (mandatory)
    const email = page.getByLabel('Email');
    if (!(await email.count())) {
      throw new Error('Email field missing');
    }
    await email.fill('<EMAIL>');

    // 6. Verify "Website" (optional)
    const website = page.getByLabel('Website');
    if (!(await website.count())) {
      throw new Error('Website field missing');
    }
    // leave blank

    // 7. Click "Submit"
    const submitBtn = page.getByRole('button', { name: 'Submit' });
    if (!(await submitBtn.count())) {
      throw new Error('Submit button missing');
    }
    await expect(submitBtn).toBeEnabled({ timeout: 10_000 });
    await submitBtn.click();

    // 8. Verify no "error" text appears after submission
    const errorLocator = page.getByText(/error/i);
    // Wait a short moment for any error to render
    await page.waitForTimeout(2_000);
    const errorCount = await errorLocator.count();
    if (errorCount > 0) {
      throw new Error('Form submission failed');
    }
    // Final assertion for clarity
    await expect(errorLocator).toHaveCount(0);
  });
});