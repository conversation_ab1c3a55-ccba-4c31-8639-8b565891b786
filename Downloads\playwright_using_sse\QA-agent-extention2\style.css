body {
  font-family: Arial, sans-serif;
  width: 400px;
  padding: 10px;
}

.container {
  text-align: center;
  width: 400px;
  padding: 20px;
}

h2 {
  font-size: 18px;
  margin-bottom: 10px;
  color: #333;
  margin-bottom: 20px;
}

input {
  width: 80%;
  padding: 8px;
  margin-bottom: 10px;
}

button {
  padding: 8px 16px;
  background-color: #4CAF50;
  color: white;
  border: none;
  cursor: pointer;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 15px;
}

button:hover {
  background-color: #45a049;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.status {
  margin: 10px 0;
  padding: 10px;
  border-radius: 4px;
  background-color: #f8f9fa;
}

#status {
  margin: 10px 0;
  font-style: italic;
}

.output {
  text-align: left;
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 4px;
  font-family: monospace;
}

.error {
  color: #dc3545;
  margin: 5px 0;
}

h3 {
  color: #666;
  margin: 10px 0;
}

p {
  margin: 5px 0;
  line-height: 1.4;
}

#results {
  text-align: left;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ccc;
  padding: 10px;
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 10px;
}

th, td {
  border: 1px solid #ccc;
  padding: 5px;
  text-align: left;
}

th {
  background-color: #f0f0f0;
}

ul {
  list-style-type: none;
  padding: 0;
}

li {
  margin: 5px 0;
}