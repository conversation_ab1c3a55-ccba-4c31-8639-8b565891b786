# mcp_runner.py

import subprocess
import threading

def run_mcp_in_background(port: int = 8931):
    """
    Runs Playwright MCP in a separate background thread on the given port.
    Returns the subprocess.Popen object.
    """

    def target():
        process = subprocess.Popen(
        f"npx @playwright/mcp@latest --port {port}",
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        shell=True, 
    )

        print(f"[MCP:{port}] Started (pid={process.pid})")

    thread = threading.Thread(target=target, daemon=True)
    thread.start()

    return thread 
