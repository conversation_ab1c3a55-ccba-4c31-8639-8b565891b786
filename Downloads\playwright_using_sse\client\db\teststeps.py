import uuid
import psycopg2
from datetime import datetime
from dotenv import load_dotenv
import os
import logging

# Load environment variables from .env file
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# Database config dictionary
DB = {
    "dbname": "QATOOL",
    "user": "postgres",
    "password": os.getenv("DB_PASSWORD"),
    "host": "localhost",
    "port": "5432"
}

def insert_test_step(testcase_id: str, steps_text: str):
    """
    Insert a test step record into the TestSteps table.
    
    Args:
        testcase_id (str): UUID string of the related test case.
        steps_text (str): Text content of the test steps.
    """
    try:
        with psycopg2.connect(**DB) as conn:
            with conn.cursor() as cur:
                step_id = str(uuid.uuid4())
                cur.execute("""
                    INSERT INTO TestSteps (step_id, testcase_id, steps, created_at)
                    VALUES (%s, %s, %s, NOW())
                """, (step_id, testcase_id, steps_text))
                logging.info(f"Inserted test step for testcase_id: {testcase_id}")
    except Exception as e:
        logging.error(f"Failed to insert test step for testcase_id {testcase_id}: {e}")
        raise

def fetch_existing_teststep_ids(project_name, link_url):
    """
    Fetch testcase_ids from TestSteps that belong to a specific project and URL.
    """
    with psycopg2.connect(**DB) as conn:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT ts.testcase_id
                FROM TestSteps ts
                JOIN TestCases tc ON ts.testcase_id = tc.testcase_id
                JOIN Projects p ON tc.project_id = p.project_id
                JOIN ProjectLinks pl ON tc.link_id = pl.link_id
                WHERE p.project_name = %s AND pl.url = %s
            """, (project_name, link_url))
            return {row[0] for row in cur.fetchall()}
