import { test, expect, Page } from '@playwright/test';

test.describe('Form2-4y5z Onrender.com: Happy-path Submission', () => {

  test.beforeEach(async ({ page }) => {
    // 1. Navigate and verify URL
    await page.goto('https://form2-4y5z.onrender.com/');
    await expect(page, 'Page failed to load')
      .toHaveURL('https://form2-4y5z.onrender.com/', { timeout: 10000 });
  });

  test('Should submit form without errors when optional fields are blank', async ({ page }) => {
    // 2. First Name: optional
    const firstName = page.getByLabel('First Name');
    await expect(firstName, 'First Name field missing').toBeVisible({ timeout: 5000 });
    await expect(firstName, 'First Name field should be optional').not.toHaveAttribute('required');

    // 3. Last Name: optional
    const lastName = page.getByLabel('Last Name');
    await expect(lastName, 'Last Name field missing').toBeVisible({ timeout: 5000 });
    await expect(lastName, 'Last Name field should be optional').not.toHaveAttribute('required');

    // 4. Phone Number: enter value
    const phone = page.getByLabel('Phone Number');
    await expect(phone, 'Phone Number field missing').toBeEditable({ timeout: 5000 });
    await phone.fill('123456789');

    // 5. Email: enter value
    const email = page.getByLabel('Email');
    await expect(email, 'Email field missing').toBeEditable({ timeout: 5000 });
    await email.fill('<EMAIL>');

    // 6. Website: optional
    const website = page.getByLabel('Website');
    await expect(website, 'Website field missing').toBeVisible({ timeout: 5000 });
    await expect(website, 'Website field should be optional').not.toHaveAttribute('required');

    // 7. Gender: select 'Male'
    // Assuming gender options are radio buttons with accessible names
    const maleRadio = page.getByRole('radio', { name: 'Male' });
    await expect(maleRadio, 'Gender field missing').toBeEnabled({ timeout: 5000 });
    await maleRadio.check();

    // 8. Submit button: click
    const submit = page.getByRole('button', { name: 'Submit' });
    await expect(submit, 'Submit button missing').toBeEnabled({ timeout: 5000 });
    await submit.click();

    // 9. Verify no "error" text appears
    // Searching entire document via body locator as fallback
    const body = page.locator('body');
    await expect(body, 'Error message detected after form submission')
      .not.toContainText(/error/i, { timeout: 10000 });
  });

});