from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
import asyncio
from dotenv import load_dotenv
import os
import re
import subprocess
import time
import openai
from mcp_use import MCPAgent, MCPClient
import sys
from langchain_openai import ChatOpenAI
from openai import OpenAI
import pandas as pd
from testcase_extractor import *
from llm_testcase_generator import *
from port_finder import *
from pathlib import Path
from server_run import run_mcp_in_background
import platform
from retriever import ask_playwright_docs
from documentation_fetcher import process_csv_data
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()
openai.api_key = os.getenv("OPENAI_API_KEY")

# MCP configuration
config_file = "mcp-playwright.json"
directory = os.getcwd()
is_windows = sys.platform.startswith("win")
run_mcp_in_background(port=8931)

# Initialize session state equivalent
session_state = {
    "session_state_info": {"success": []},
    "chat_history": [],
    "intermediate_test": None,
    "test_cases_ready": False,
}

# System prompt (same as original)
system_prompt = """
You are a QA testing assistant powered by Playwright via MCP.

You will be given a Playwright documentation guide with tested patterns and code snippets. Use this guide to inform your approach—reference its examples, follow its structure, and adapt them to the specific user request. When in doubt, refer to the documentation and prefer partial matches only if they are unique and reliable.

When a user provides a URL and a specific field to validate (e.g., "first name"), follow these steps:

1. Navigate to the provided URL.
2. Locate the field using only real accessibility information:
   - Use only actual content from the page's accessibility tree.
   - Prefer exact `label` text, `placeholder`, `role` + accessible name, or unique visible text.
   - Never guess or fabricate selectors.
   - Avoid dynamic IDs, auto-generated classes, or CSS selectors unless no accessible option exists.
3. After entering test input:
   - Submit the form.
   - Observe any UI feedback, such as validation messages, highlights, or alerts.
4. Log for each test:
   - The test input
   - Expected behavior
   - Actual observed behavior
   - Validation or error message (if any)
5. After all inputs are tested:
   - Summarize results clearly under labeled sections (e.g., Test 1: Empty Input)
   - Use headings, spacing, and structure for readability.
   - Include a "Playwright Test Code" section at the end with the full, reusable code used.
6. Then, generate a clean, grouped test report:
   - Each block must include:
     * Test title (e.g., "Missing Required Field")
     * Status (PASSED / FAILED)
     * Observations or messages
     * Screenshot (if relevant)
     * Code snippet used (optional)

Selector Guidelines:
- Only use what’s actually present in the DOM’s accessibility tree
- Always confirm labels, placeholders, or roles before using them
- Use partial matches only when the match is unique and consistent
- Avoid unnecessary selector chaining

Validation Handling:
- Confirm visibility of messages using `expect(locator).toBeVisible()`
- Confirm content using `expect(locator).toContainText(...)`
- Avoid fragile or promise-based patterns like `.textContent()`

Your goal is to generate accurate, accessibility-first tests that mirror what a real user would experience.
"""

def build_system_prompt() -> str:
    system_prompt = (
        "You are an expert Playwright Test Automation Engineer specialized in automation."
        "An expert at converting written test steps into clean, maintainable, and efficient code."
        "You don’t just read text literally—you grasp its underlying semantic meaning"
        "Your mission is to generate robust, maintainable test suites that leverage Playwright's Model-Centered Protocol (MCP). "
        "CRITICAL: MCP utilizes the accessibility tree rather than the full DOM - all element interactions must use "
        "accessibility attributes instead of traditional DOM structure.\n\n"
 
        "<core_capabilities>\n"
        "- Create production-ready Playwright test suites in TypeScript\n"
        "- Work exclusively with accessibility tree attributes (not DOM)\n"
        "- Generate self-contained, executable test code with proper assertions\n"
        "- Implement comprehensive error handling and recovery mechanisms\n"
        "- Produce clear test documentation with rationales for implementation choices\n"
        "</core_capabilities>\n\n"
 
        "<locator_strategy>\n"
        "PREFERRED (use in this order):\n"
        "1. `getByLabel`: Target elements by their associated label text\n"
        "2. `getByRole`: Target elements by their ARIA role with appropriate options\n"
        "3. `getByText`: Target elements by their visible text content\n"
        "4. `getByPlaceholder`: Target input fields by placeholder text\n"
        "AVOID THESE (use only as documented last resort):\n"
        "- CSS selectors, XPath, ID selectors, or any DOM structure-dependent locators\n"
        "- If you must use a non-accessibility locator, document the justification\n"
        "</locator_strategy>\n\n"

        "<reasoning_steps>\n"
        "Step 1: Reason About the Goal\n"
        "  - Analyze the user's step descriptions to identify the intent.\n"
        "  - Determine the core action (input, click, verify) and desired outcome.\n"
        "  - If locator info is missing or ambiguous, infer only what's necessary using accessibility principles.\n"
        "  - Do not invent new behaviors or extend beyond the provided instructions.\n\n"
        "Step 2: Contextually dynamic fields-When interpreting test steps that involve form inputs, recognize that some fields are dynamic in a **real-world sense**, meaning their valid values depend on business logic, temporal context, or test intent — not just DOM structure." 
        "Follow these guidelines:"
        "1. **Respect Business Rules**:"
        "   - For age restrictions (e.g., must be 18+), compute the correct date relative to today's date."
        "   - For fields like expiration dates, use future dates unless the test requests otherwise."
        "2. **Use realistic, synthetic data**:"
        "   - Names, emails, phone numbers, etc., should be plausible and locale-appropriate."
        "   - Invalid values should be intentionally incorrect but syntactically relevant"
        "Examples:"
        "Test Step: 'Enter a valid date of birth for a user who is at least 18 years old."
        "find current date minus 18 years add to the date of birth field"
        "Step 3: Semantic reasoning -Proper semantic understanding is crucial for ensuring accurate test execution, especially when using LLM-driven test frameworks. The following gives semantic misunderstandings "
        "   1. **'Empty' Inputs**:"
        "       - Interpret 'empty' as an empty string: `\"\"` (i.e., leave the field blank)."
        "       - Do NOT type the word 'empty'."
        "   2. **'Visible' vs. 'Hidden'**:"
        "       - 'Visible' means the element should be rendered and visible on screen."
        "       - 'Hidden' refers to elements not visible, either via `display: none` or `visibility: hidden`."
        "Step 4: Plan the Actions\n"
        "  - Break each goal into sequenced Playwright actions.\n"
        "  - Prefer visible text and ARIA-based locators.\n"
        "  - Include:\n"
        "    * Navigations (to pages or forms)\n"
        "    * Inputs (values for fields with labels)\n"
        "    * Actions (clicks on accessible buttons)\n"
        "    * Verifications (assertions on state or message visibility)\n"
        "  - Insert waits for navigation, element visibility, or interaction readiness.\n"
        "  - Handle challenges (e.g., duplicate labels, async rendering) using fallback strategies.\n\n"
        "Step 5: Self-Correct and Validate\n"
        "  - Review action plan for alignment with input steps.\n"
        "  - Ensure only the intended validations are present.\n"
        "  - Avoid overchecking (e.g., asserting success when only error is expected).\n"
        "  - Consider edge cases (missing/hidden labels, race conditions).\n"
        "  - Adjust to align with accessibility constraints and test determinism.\n\n"
        "Step 6: Generate Code (Playwright + TypeScript)\n"
        "  - Use `test.describe`, `test.beforeEach`, and multiple `test()` blocks.\n"
        "  - Use `await expect()` with meaningful selectors and accessible text.\n"
        "  - Structure test files cleanly with fixtures and helper utilities if needed.\n"
        "  - Name tests clearly (e.g., 'should show error for invalid email').\n"
        "  - Include comments, typed inputs, and properly formatted assertions.\n"
        "  - Ensure code is fully standalone and executable.\n"
        "  - Take screenshot and save to output directory.\n"
        "  - Save the test code to the output directory.\n"
        "</reasoning_steps>\n\n"
 
        "<testing_methodology>\n"
        "1. Requirement Analysis\n"
        "   - Extract clear test objectives from requirements\n"
        "   - Identify critical user flows and validation points\n"
        "   - Consider accessibility implications in test design\n\n"
        "2. Test Action Planning\n"
        "   - Design clear test step sequences with accessibility-first approach\n"
        "   - Anticipate potential stability issues and plan mitigations\n"
        "   - Structure tests for readability and maintenance\n\n"
        "3. Implementation Best Practices\n"
        "   - Implement page objects or component abstractions when beneficial\n"
        "   - Use descriptive test and function names that reflect business logic\n"
        "   - Include appropriate waits and synchronization points\n"
        "   - Add comprehensive assertions that validate both state and content\n"
        "   - Implement error recovery mechanisms for fragile interactions\n\n"
        "4. Validation Strategy\n"
        "   - Form field validation (empty, invalid, valid inputs)\n"
        "   - Error message verification via accessibility attributes\n"
        "   - For fields do not use exact text match, use partial matches or regex where appropriate\n"
        "   - Visual validation where appropriate\n"
        "5. Guidelines:\n"
        "   -Use the tool named <browser_generate_playwright_test> to generate test cases.\n"
        "   -Do not use promises or response checks unless explicitly specified.\n"
        "   -Refrain from adding any extra validations that are not explicitly stated in the test steps.\n"
        "   -Avoid capturing unnecessary details from the website that are not outlined in the test steps.\n"
        "   -Use timeout/wait in milliseconds example 5000,10000\n"
        "6. When verifying dynamic text content (e.g., error messages, validation hints, or status updates), always ensure the target element is scrolled into view using scrollIntoViewIfNeeded() or equivalent. This guarantees visibility for both the automation tool and visual validation.\n"
        "</testing_methodology>\n\n"
 
        "<complex_scenarios_handling>\n"
        "- Dynamic content: Implement `waitForSelector` with appropriate timeout and state options\n"
        "- Shadow DOM: Use special frame handling capabilities in Playwright\n"
        "- iframes: Leverage `frameLocator()` with accessibility selectors\n"
        "- SPAs: Add stabilization waits and state verification before actions\n"
        "- Multi-step forms: Implement progressive form filling with validation at each step\n"
        "- Internationalization: Create parameterized tests that work across locales\n"
        "</complex_scenarios_handling>\n\n"
 
        "<code_structure>\n"
        "- Test files should follow a consistent organization pattern\n"
        "- Include setup/teardown with appropriate fixture management\n"
        "- Implement reusable helper functions for common operations\n"
        "- Use appropriate test annotations and metadata\n"
        "- Follow TypeScript best practices with proper typing\n"
        "- Implement appropriate error handling with diagnostic information\n"
        "</code_structure>\n\n"
 
        "<output_format>\n"
        "1. Test Strategy Summary (concise overview of approach)\n"
        "2. Playwright Test Code (complete, runnable TypeScript code)\n"
        "3. Implementation Notes (key design decisions, accessibility considerations)\n"
        "4. Potential Enhancements (suggestions for improved coverage or robustness)\n"
        "5. Finally save the code in a file named playwright.spec.ts\n"
        "</output_format>\n\n"
 
        "<communication_style>\n"
        "- Use formal, professional language appropriate for technical documentation\n"
        "- Present code with clear formatting and proper syntax highlighting\n"
        "- When explaining code choices, focus on technical rationale and best practices\n"
        "- Highlight accessibility considerations in your implementation decisions\n"
        "- Provide complete context for code decisions rather than fragmented explanations\n"
        "</communication_style>\n"
        
        "<reference>\n"
        "-Use the provided Playwright documentation as a reference for best practices and code snippets .\n"
        " -Use the test cases as a source of truth for the test steps and expected behavior.\n"
        " -No additional checks should be performed other than the ones mentioned in the test steps.\n"
        "- Do not invent new behaviors or extend beyond the provided instructions.\n"
        "- Test cases are well written so no need to add any extra validations that are not explicitly stated in the test steps.\n"
    )
    return system_prompt

def add_instructions(test_step_docuemtation):
    instruction=(
f"Here are the test steps and documentation {test_step_docuemtation} for Playwright MCP to convert to optimal automation script.Give priority to the test steps and refer to the documentation only as a guide. Documentation may add extra information that is not needed for the test steps. Do not add any extra validations that are not explicitly stated in the test steps. Do not invent new behaviors or extend beyond the provided instructions. Test cases are well written so no need to add any extra validations that are not explicitly stated in the test steps.Specifically any text validations out of scope of the test steps should not be added. Use the documentation only as a guide and not as a source of truth for the test steps. Do not add any extra validations that are not explicitly stated in the test steps. Do not invent new behaviors or extend beyond the provided instructions. Test cases are well written so no need to add any extra validations that are not explicitly stated in the test steps.")
    return instruction

# Initialize MCPAgent
client = MCPClient.from_config_file(config_file)
llm = ChatOpenAI(
    model="o4-mini-2025-04-16",
    temperature=1,
    reasoning_effort="low",
)
session_state["agent"] = MCPAgent(
    llm=llm,
    client=client,
    max_steps=30,
    memory_enabled=True,
    system_prompt=build_system_prompt()
)

def extract_typescript_code(response_text):
    pattern = r'Playwright Test Code.*?```(?:typescript|javascript|ts|js)\s*(.*?)```'
    match = re.search(pattern, response_text, re.DOTALL | re.IGNORECASE)
    if match:
        extract_code = match.group(1).strip()
        return extract_code
    return None

def open_playwright_report(directory=directory, is_windows=is_windows):
    try:
        logger.info("\nChecking port 9323 with all methods:")
        is_port_in_use(9323)
        logger.info("\nFinding available port:")
        port = find_available_port(9323)
        logger.info(f"Final port to use: {port}")
        command = ["npx", "playwright", "show-report", f"--port={port}"]
        startupinfo = None
        if is_windows:
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        if is_windows:
            subprocess.Popen(
                f'start cmd /c {" ".join(command)}',
                cwd=directory,
                shell=True,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                startupinfo=startupinfo
            )
        else:
            subprocess.Popen(
                command,
                cwd=directory,
                stdout=subprocess.DEVNULL,
                stderr=subprocess.DEVNULL,
                start_new_session=True
            )
        return f"Report launched successfully on port {port}."
    except Exception as e:
        return f"Error showing report: {str(e)}"

def save_and_run_ts_code(code, index):
    if not code:
        return "No TypeScript code found in the response."
    try:
        with open(f"{directory}/tests/test_report{index+1}.spec.ts", "w", encoding="utf-8") as f:
            f.write(code)
    except Exception as e:
        return f"Error saving TypeScript file: {str(e)}"

def count_ts_files(directory):
    return len(list(Path(directory).glob('*.ts')))

def check_ts_code_count(df):
    folder_path = Path('tests/')
    ts_file_count = count_ts_files(folder_path)
    df_row_count = len(df)
    logger.info(f"TS file count: {ts_file_count}")
    logger.info(f"DataFrame row count: {df_row_count}")
    if ts_file_count == df_row_count:
        logger.info("✅ Counts match.")
        return True
    else:
        logger.info("❌ Counts do not match.")
        return False

def check_test_code_count():
    df = pd.read_csv('ai_ready_test_steps.csv')
    return check_ts_code_count(df)

def run_playwright_tests():
    test_directory = "tests"
    is_windows = platform.system() == "Windows"
    command = ["npx", "playwright", "test", test_directory, "--config=playwright.config.ts"]
    try:
        result = subprocess.run(
            command,
            cwd=".",
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            shell=is_windows
        )
        return result.stdout, result.stderr
    except Exception as e:
        return "", str(e)

# FastAPI setup with CORS
app = FastAPI(title="Form Validation Testing API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["chrome-extension://dipdhhmkmfonehddkpamjddjfnbdcbpl", "http://localhost:8000"],  # Specific Chrome extension ID and localhost
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["Content-Type", "Authorization", "accept", "Origin", "Access-Control-Request-Method", "Access-Control-Request-Headers"],
    expose_headers=["*"],
    max_age=3600  # Cache preflight requests for 1 hour
)

class URLRequest(BaseModel):
    url: str

@app.post("/run-tests")
async def run_tests(request: URLRequest):
    response_data = {
        "status": "success",
        "message": "",
        "test_cases": [],
        "logs": [],
        "errors": []
    }
    
    try:
        url = request.url
        start_time_qa_spec = time.time()
        response_data["logs"].append("Processing the QA spec...")
        extractor_test_flag = get_text_from_google_doc(url)
        cout_checker = check_test_code_count()
        duration_qa_spec = time.time() - start_time_qa_spec
        response_data["logs"].append(f"QA Spec processing completed in {duration_qa_spec:.2f} seconds")
        session_state["session_state_info"]["success"].append(f"QA Spec processing completed in {duration_qa_spec:.2f} seconds")
        
        if not extractor_test_flag or not cout_checker:
            start_time_creating_testcase = time.time()
            response_data["logs"].append("Creating test cases...")
            reasoning_steps = ""
            gen = stream_test_case_generation(testcase_extractor.get_text_from_qaspec())
            try:
                while True:
                    token = next(gen)
                    cleaned_token = re.sub(r"^\[Step\s*\d+\]\s*", "", token)
                    reasoning_steps += cleaned_token + "\n"
            except StopIteration as e:
                full_output = e.value
                try:
                    df = save_csv_from_output(full_output, 'form_validation_test_cases.csv')
                    response_data["logs"].append("Test cases saved to test_cases.csv")
                except Exception as err:
                    response_data["errors"].append(f"Error extracting CSV: {err}")
            duration_creating_testcase = time.time() - start_time_creating_testcase
            response_data["logs"].append(f"Test case generation completed in {duration_creating_testcase:.2f} seconds")
            session_state["session_state_info"]["success"].append(f"Test case generation completed in {duration_creating_testcase:.2f} seconds")
        
        response_data["logs"].append("Processing the test cases generated")
        df = pd.read_csv('form_validation_test_cases.csv')
        time.sleep(10)
        
        start_time_cases_generated = time.time()
        response_data["logs"].append("Generating detailed test steps")
        filtered_df = filter_by_expected_result(df, no=None, type="Regression")
        logger.info(f"Processing {len(filtered_df)} rows...")
        filtered_df = filtered_df.head(1)
        filtered_df['Test_Steps_AI'] = filtered_df.apply(process_row, axis=1)
        duration_cases_generated = (time.time() - start_time_cases_generated) / 60
        response_data["logs"].append(f"Detailed test steps generation completed in {duration_cases_generated:.2f} minutes")
        session_state["session_state_info"]["success"].append(f"Detailed test steps generation completed in {duration_cases_generated:.2f} minutes")
        
        intermediate_test = filtered_df[['Test_Case_ID', 'Test_Case_Description', 'Test_Steps_AI']].rename(columns={
            'Test_Case_ID': 'No.',
            'Test_Case_Description': 'Test Description',
            'Test_Steps_AI': 'Detailed Test Steps'
        }).reset_index(drop=True)
        response_data["test_cases"] = intermediate_test.to_dict(orient="records")
        session_state["intermediate_test"] = intermediate_test
        session_state["test_cases_ready"] = True
        
        # Simulate saving edited test cases
        start_time_save_csv = time.time()
        edited_df = intermediate_test
        edited_df["No."] = [f"TC{str(i+1).zfill(3)}" for i in range(len(edited_df))]
        output_file = "ai_ready_test_steps.csv"
        edited_df.to_csv(output_file, index=False)
        logger.info(f"✅ Test cases saved to {output_file}")
        duration_save_csv = time.time() - start_time_save_csv
        response_data["logs"].append(f"Detailed test steps saved to CSV in {duration_save_csv:.2f} seconds")
        session_state["session_state_info"]["success"].append(f"Detailed test steps saved to CSV in {duration_save_csv:.2f} seconds")
        
        # Execute automation scripts
        start_time = time.time()
        prmpt = pd.read_csv('ai_ready_test_steps.csv')
        for index, row in prmpt.iterrows():
            response_data["logs"].append(f"Generating automation scripts for test case no. : {index+1}")
            test_steps = row["Detailed Test Steps"]
            docs = ask_playwright_docs(test_steps)
            refine_prompt = row["Detailed Test Steps"] + "\n\n" + docs
            references = add_instructions(refine_prompt)
            response = await session_state["agent"].run("\n\nUser Instructions:\n" + references.strip())
            session_state["chat_history"].append(("You", f"{references.strip()}"))
            session_state["chat_history"].append(("Assistant", response))
            code = extract_typescript_code(response)
            if code:
                logger.info(f"Extracted TypeScript code:")
                test_results = save_and_run_ts_code(code, index)
                response_data["logs"].append(test_results)
            else:
                session_state["chat_history"].append(("System", "No TypeScript code found in the response."))
                response_data["errors"].append("No TypeScript code found in the response.")
        
        stdout_log, stderr_log = run_playwright_tests()
        response_data["logs"].append(stdout_log)
        if stderr_log:
            response_data["errors"].append(stderr_log)
        
        report_status = open_playwright_report()
        response_data["logs"].append(report_status)
        
        duration = (time.time() - start_time) / 60
        response_data["logs"].append(f"Code generation completed in {duration:.2f} minutes")
        session_state["session_state_info"]["success"].append(f"Code generation completed in {duration:.2f} minutes")
        
        session_state["test_cases_ready"] = False
        return response_data
    
    except Exception as e:
        session_state["chat_history"].append(("Error", str(e)))
        response_data["status"] = "error"
        response_data["errors"].append(str(e))
        raise HTTPException(status_code=500, detail=str(e))