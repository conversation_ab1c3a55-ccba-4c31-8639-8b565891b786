# ingest.py
from dotenv import load_dotenv
load_dotenv() # Load environment variables from .env file

import json
import chromadb
import os
import google.generativeai as genai
import time

# --- Configuration ---
# Get the Google API Key from environment variables (now loaded by dotenv)
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
if not GOOGLE_API_KEY:
    print("Error: GOOGLE_API_KEY environment variable not set.")
    print("Please ensure you have a .env file in the same directory with GOOGLE_API_KEY='YOUR_API_KEY'")
    exit()

# Configure the generative AI library
genai.configure(api_key=GOOGLE_API_KEY)

# Specify the Gemini embedding model to use
EMBEDDING_MODEL = "models/text-embedding-004"

# Path to your JSON data file (generated by the scraper)
DATA_FILE = "playwright_docs.json"

# Path for your ChromaDB database
DB_PATH = "./playwright_vector_db"

# Name of the ChromaDB collection
COLLECTION_NAME = "playwright_docs"

# --- Data Loading ---
print(f"Loading data from {DATA_FILE}...")
try:
    # Use encoding='utf-8' for robustness
    with open(DATA_FILE, "r", encoding='utf-8') as f:
        docs = json.load(f)
    print(f"Successfully loaded {len(docs)} documents from {DATA_FILE}.")
    # Note: This file should contain documentation filtered for UI testing by the scraper.
except FileNotFoundError:
    print(f"Error: {DATA_FILE} not found. Please run the scraping script first to generate this file.")
    exit()
except json.JSONDecodeError:
    print(f"Error: Could not decode JSON from {DATA_FILE}. Check file format.")
    exit()
except Exception as e:
    print(f"An unexpected error occurred while loading data: {e}")
    exit()

# --- ChromaDB Setup ---
print(f"Initializing ChromaDB client at {DB_PATH}...")
try:
    client = chromadb.PersistentClient(path=DB_PATH)
    # Create or get collection
    collection = client.get_or_create_collection(name=COLLECTION_NAME)
    print(f"Successfully connected to or created collection '{COLLECTION_NAME}'.")
except Exception as e:
    print(f"Error initializing ChromaDB or getting collection: {e}")
    exit()

# --- Embedding and Storage ---
print(f"Starting embedding and storing documents using model: {EMBEDDING_MODEL}...")

# Check if the collection is empty before adding to avoid duplicates on reruns
# For updates, you might need a more sophisticated strategy (e.g., delete and re-add, or check IDs)
if collection.count() > 0:
    print(f"Collection '{COLLECTION_NAME}' already contains {collection.count()} documents.")
    print("Skipping ingestion to avoid duplicates. Delete the database directory ('./playwright_vector_db') and rerun if you want to re-embed.")
else:
    start_time = time.time()
    documents_to_add = []
    metadatas_to_add = []
    ids_to_add = []
    embeddings_to_add = []

    # Batch size for adding to ChromaDB - adjust based on memory/performance
    BATCH_SIZE = 200

    for idx, doc in enumerate(docs):
        # Create a unique ID for the document
        # Using a hash of the URL could be more robust than index if the source list order changes
        # doc_id = hashlib.sha256(doc.get('url', f'doc_{idx}').encode('utf-8')).hexdigest()
        doc_id = f"doc_{idx}" # Using index for simplicity as before

        try:
            # Combine text fields for embedding
            # Ensure all fields are strings and handle missing keys/None values gracefully
            title_text = str(doc.get('title', '') or '')
            headings_text = ' '.join(str(h or '') for h in doc.get('headings', []))
            paragraphs_text = ' '.join(str(p or '') for p in doc.get('paragraphs', []))
            code_snippets_text = ' '.join(str(c or '') for c in doc.get('code_snippets', []))

            text_to_embed = f"{title_text}\n{headings_text}\n{paragraphs_text}\n{code_snippets_text}".strip()

            # Ensure the text is not empty before embedding
            if not text_to_embed:
                 print(f"Skipping empty document data at index {idx} (ID: {doc_id}).")
                 continue

            # Generate embedding using Google's gemini model
            embedding_response = genai.embed_content(
                model=EMBEDDING_MODEL,
                content=text_to_embed,
                task_type="RETRIEVAL_DOCUMENT" # Recommended task type for RAG
            )
            embedding = embedding_response['embedding'] # Extract the list of floats

            documents_to_add.append(text_to_embed)
            metadatas_to_add.append({
                "url": doc.get("url", ""),
                "title": doc.get("title", "")
            })
            ids_to_add.append(doc_id)
            embeddings_to_add.append(embedding)

            # Add in batches to improve performance
            if len(ids_to_add) >= BATCH_SIZE:
                 print(f"Adding batch of {len(ids_to_add)} documents (up to index {idx})...")
                 collection.add(
                     documents=documents_to_add,
                     metadatas=metadatas_to_add,
                     ids=ids_to_add,
                     embeddings=embeddings_to_add
                 )
                 # Clear lists for the next batch
                 documents_to_add = []
                 metadatas_to_add = []
                 ids_to_add = []
                 embeddings_to_add = []

        except Exception as e:
            print(f"Error processing document {idx} (ID: {doc_id}): {e}")
            # Optionally print document details for debugging:
            # print(f"Problematic document data: {doc}")
            continue # Continue with the next document

    # Add any remaining documents in the last batch
    if ids_to_add:
        print(f"Adding final batch of {len(ids_to_add)} documents...")
        collection.add(
            documents=documents_to_add,
            metadatas=metadatas_to_add,
            ids=ids_to_add,
            embeddings=embeddings_to_add
        )

    end_time = time.time()
    print("Embedding and storage process complete.")
    print(f"Total documents attempted from JSON: {len(docs)}")
    print(f"Total documents successfully added/present in collection: {collection.count()}") # Note: This counts *all* docs, including previous runs if not cleared
    print(f"Time taken: {end_time - start_time:.2f} seconds")