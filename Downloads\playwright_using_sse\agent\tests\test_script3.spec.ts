import { test, expect } from '@playwright/test';

test.describe('Form Field Presence & Validation Errors', () => {
  const URL = 'https://emlabsform.onrender.com/';
  const LOAD_TIMEOUT = 10000;

  test.beforeEach(async ({ page }) => {
    // 1. Navigate and verify page load
    const response = await page.goto(URL, { timeout: LOAD_TIMEOUT, waitUntil: 'load' });
    await expect(response, 'Page failed to load').not.toBeNull();
  });

  test('Initial Fields Present & Empty', async ({ page }) => {
    // Field definitions
    const labels = [
      'First Name',
      'Last Name',
      'Phone Number',
      'Email',
      'Website',
      'Date of Birth',
    ];

    for (const labelText of labels) {
      // 2–7. Locate by label
      const input = page.getByLabel(labelText);
      await expect(input, `${labelText} field missing`).toBeVisible();
      // Verify it's empty
      await expect(input).toHaveValue('');
    }
  });

  test('Validation Errors for Phone, Email, Date of Birth', async ({ page }) => {
    // 8. Click Submit
    const submitButton = page.getByRole('button', { name: 'Submit' });
    await expect(submitButton).toBeVisible();
    await submitButton.click();
    // Wait briefly for validation messages
    await page.waitForTimeout(500); 

    // 9. Phone Number error
    const phoneError = page.getByText('Enter a number for this field.');
    await expect(phoneError, 'Phone Number error not displayed').toBeVisible();

    // 10. Email error
    const emailError = page.getByText(
      'Enter a valid email address. (eg: <EMAIL>)'
    );
    await expect(emailError, 'Email error not displayed').toBeVisible();

    // 11. Date of Birth error
    const dobError = page.getByText(
      "Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18."
    );
    await expect(dobError, 'Date of Birth error not displayed').toBeVisible();
  });
});