import { test, expect } from '@playwright/test';

test.describe('EMLabs Form – Full Submission Flow', () => {
  const FORM_URL = 'https://emlabsform.onrender.com/';

  test('should fill all fields and display success message', async ({ page }) => {
    // Step 1: Navigate to the form page
    try {
      await page.goto(FORM_URL, { waitUntil: 'load', timeout: 10_000 });
    } catch {
      throw new Error('Form page failed to load');
    }
    // Confirm URL exactly matches
    await expect(page).toHaveURL(FORM_URL, { timeout: 10_000 });

    // Helper to wait for and return a locator
    const getField = async (label: string, errorMsg: string) => {
      const locator = page.getByLabel(label);
      try {
        await locator.waitFor({ state: 'visible', timeout: 5_000 });
        return locator;
      } catch {
        throw new Error(errorMsg);
      }
    };

    // Step 2: First Name
    const firstName = await getField('First Name', 'First Name field missing');
    await firstName.fill('JohnDOE');

    // Step 3: Last Name
    const lastName = await getField('Last Name', 'Last Name field missing');
    await lastName.fill('Smith');

    // Step 4: Phone Number
    const phone = await getField('Phone Number', 'Phone Number field missing');
    await phone.fill('987654321');

    // Step 5: Email
    const email = await getField('Email', 'Email field missing');
    await email.fill('<EMAIL>');

    // Step 6: Website
    const website = await getField('Website', 'Website field missing');
    await website.fill('www.domain.com');

    // Step 7: Submit
    const submitBtn = page.getByRole('button', { name: 'Submit' });
    try {
      await submitBtn.waitFor({ state: 'visible', timeout: 5_000 });
    } catch {
      throw new Error('Submit button missing');
    }
    await submitBtn.click();

    // Step 8: Verify success message
    const successMsg = page.getByText(/success/i);
    try {
      await successMsg.waitFor({ state: 'visible', timeout: 10_000 });
    } catch {
      throw new Error('Form submission failed');
    }
    // Final assertion
    await expect(successMsg).toBeVisible();
  });
});