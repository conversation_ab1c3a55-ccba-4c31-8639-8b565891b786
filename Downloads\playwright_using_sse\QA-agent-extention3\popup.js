document.addEventListener('DOMContentLoaded', () => {
  const commandInput = document.getElementById('commandInput');
  const submitButton = document.getElementById('submitButton');
  const statusDiv = document.getElementById('status');
  const resultsdescDiv = document.getElementById('results');

  submitButton.addEventListener('click', async () => {
    const command = commandInput.value.trim().toLowerCase();
    if (command !== 'generate test cases') {
      statusDiv.textContent = 'Please type "generate test cases"';
      resultsdescDiv.innerHTML = '';
      return;
    }

    statusDiv.textContent = 'Fetching current tab URL...';
    try {
      // Get the current tab's URL
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      const currentUrl = tabs[0].url;

      statusDiv.textContent = 'Calling API...';
      const response = await fetch('http://127.0.0.1:8000/generate-tests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: currentUrl, message: command }),
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`);
      }

      const data = await response.json();
      statusDiv.textContent = `Status: ${data.status}`;

       // Display only Playwright scripts and errors
      let output = '';

      // Display TypeScript scripts
      if (data.typescript_scripts && data.typescript_scripts.length > 0) {
        output += '<h3>Generated Playwright Scripts</h3>';
        data.typescript_scripts.forEach(script => {
          output += `<div><strong>Test Case ID: ${script.test_case_id}</strong><pre><code>${script.typescript_code}</code></pre></div>`;
        });
      }

     

      // Display errors
      if (data.errors && data.errors.length > 0) {
        output += '<h3>Errors</h3><ul>';
        data.errors.forEach(error => {
          output += `<li>${error}</li>`;
        });
        output += '</ul>';
      }

      resultsdescDiv.innerHTML = output;
    } catch (error) {
      statusDiv.textContent = 'Error';
      if (error.message.includes('Failed to fetch')) {
        resultsdescDiv.innerHTML = '<p class="error">Could not connect to the Test Agent API. Please ensure the server is running on http://127.0.0.1:8000.</p>';
      } else {
        resultsdescDiv.innerHTML = `<p class="error">Error: ${error.message}</p>`;
      }
    }
  });
});