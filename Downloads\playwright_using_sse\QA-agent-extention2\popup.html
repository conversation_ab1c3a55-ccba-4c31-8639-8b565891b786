<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Test Agent Extension</title>
  <link rel="stylesheet" href="style.css">
</head>
<body>
  <div class="container">
    <h2>Test Agent Extension</h2>
    <input id="commandInput" type="text" placeholder="Type 'generate test cases'">
    <button id="submitButton">Submit</button>
    <div id="status" class="status">Status: Waiting for input...</div>
    <div id="results"></div>
  </div>
  <script src="popup.js"></script>
</body>
</html>