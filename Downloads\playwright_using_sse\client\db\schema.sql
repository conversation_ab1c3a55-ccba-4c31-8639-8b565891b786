
CREATE TABLE Projects (
    project_id UUID PRIMARY KEY,
    project_name TEXT NOT NULL UNIQUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE ProjectFiles (
    file_id UUID PRIMARY KEY,
    project_id UUID NOT NULL,
    filename TEXT NOT NULL,
    s3_link TEXT NOT NULL,
    uploaded_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (project_id) REFERENCES Projects(project_id)
);

CREATE TABLE ProjectLinks (
    link_id UUID PRIMARY KEY,
    project_id UUID NOT NULL,
    file_id UUID,
    url TEXT NOT NULL,
    page_title TEXT,
    scraped_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (project_id) REFERENCES Projects(project_id),
    FOREIGN KEY (file_id) REFERENCES ProjectFiles(file_id)
);


CREATE TABLE TestCases (
    testcase_id UUID PRIMARY KEY,
    project_id UUID NOT NULL,
    link_id UUID NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (project_id) REFERENCES Projects(project_id),
    FOREIGN KEY (link_id) REFERENCES ProjectLinks(link_id)
);


CREATE TABLE TestSteps (
    step_id UUID PRIMARY KEY,
    testcase_id UUID NOT NULL UNIQUE,
    steps TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (testcase_id) REFERENCES TestCases(testcase_id)
);



CREATE TABLE TestRuns (
    run_id UUID PRIMARY KEY,
    project_id UUID NOT NULL,
    run_name TEXT,
    run_started_at TIMESTAMP DEFAULT NOW(),
    run_ended_at TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES Projects(project_id)
);


CREATE TABLE AutomationScripts (
    script_id UUID PRIMARY KEY,
    step_id UUID NOT NULL,
    step_number INT NOT NULL,
    language TEXT,
    code TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (step_id) REFERENCES TestSteps(step_id)
);


CREATE TABLE TestRunScripts (
    id UUID PRIMARY KEY,
    run_id UUID NOT NULL,
    script_id UUID NOT NULL,
    script_version TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    FOREIGN KEY (run_id) REFERENCES TestRuns(run_id),
    FOREIGN KEY (script_id) REFERENCES AutomationScripts(script_id)
);


CREATE TABLE TestResults (
    result_id UUID PRIMARY KEY,
    run_id UUID NOT NULL,
    step_id UUID NOT NULL,
    step_number INT NOT NULL,
    testrunscript_id UUID NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('PASS', 'FAIL', 'SKIPPED')),
    executed_at TIMESTAMP DEFAULT NOW(),
    duration_seconds INT,
    remarks TEXT,
    FOREIGN KEY (run_id) REFERENCES TestRuns(run_id),
    FOREIGN KEY (step_id) REFERENCES TestSteps(step_id),
    FOREIGN KEY (testrunscript_id) REFERENCES TestRunScripts(id)
);
