import requests
import xml.etree.ElementTree as ET
from playwright.sync_api import sync_playwright
import json
import time
import sys

def fetch_sitemap_urls(sitemap_url):
    """Fetches URLs from a sitemap."""
    print(f"Fetching sitemap from {sitemap_url}...")
    try:
        response = requests.get(sitemap_url, timeout=10)
        response.raise_for_status()
        root = ET.fromstring(response.content)
        urls = [elem.text for elem in root.iter('{http://www.sitemaps.org/schemas/sitemap/0.9}loc') if elem.text]
        print(f"Found {len(urls)} URLs in sitemap.")
        return urls
    except requests.exceptions.RequestException as e:
        print(f"Error fetching sitemap {sitemap_url}: {e}", file=sys.stderr)
        return []
    except ET.ParseError as e:
        print(f"Error parsing sitemap XML from {sitemap_url}: {e}", file=sys.stderr)
        return []
    except Exception as e:
        print(f"An unexpected error occurred fetching sitemap {sitemap_url}: {e}", file=sys.stderr)
        return []

def filter_docs_urls(urls):
    """Filters URLs to keep only those relevant for web testing, excluding Android-related pages."""
    # Step 1: Filter for /docs/ URLs and exclude Android-related ones immediately
    docs_urls = [url for url in urls if '/docs/' in url and '/android' not in url.lower()]
    print(f"Filtered down to {len(docs_urls)} /docs/ URLs (excluding Android).")

    # Step 2: Include only URLs related to API and web UI testing
    relevant_urls = [
        url for url in docs_urls
        if ('/docs/test-' in url) or ('/docs/api-testing' in url) or ('/docs/api/' in url)
    ]

    # Step 3: Exclude general documentation pages
    exclude_general = ['/docs/intro', '/docs/getting-started', '/docs/core-concepts']
    filtered_urls = [
        url for url in relevant_urls
        if not any(exclude_path in url for exclude_path in exclude_general)
    ]

    print(f"Further filtered to {len(filtered_urls)} URLs relevant for web testing.")
    return filtered_urls
def extract_documentation(page, url):
    """Navigates to a URL and extracts documentation content using a Playwright page."""
    try:
        page.goto(url, wait_until="domcontentloaded", timeout=45000)
        title = page.title()

        js_extract_content = """
            (mainContentSelector) => {
                const scope = document.querySelector(mainContentSelector) || document;
                const heading_elements = scope.querySelectorAll('h1, h2, h3');
                const paragraph_elements = scope.querySelectorAll('p');
                const code_elements = scope.querySelectorAll('pre code, code');

                const headings = Array.from(heading_elements)
                    .map(el => el.textContent ? el.textContent.trim() : '')
                    .filter(text => text);

                const paragraphs = Array.from(paragraph_elements)
                    .map(el => el.textContent ? el.textContent.trim() : '')
                    .filter(text => text);

                const code_snippets = Array.from(code_elements)
                    .map(el => el.textContent ? el.textContent.trim() : '')
                    .filter(text => text);

                return {
                    headings: headings,
                    paragraphs: paragraphs,
                    code_snippets: code_snippets
                };
            }
        """

        main_content_selector = 'article.markdown'
        extracted_data = page.evaluate(js_extract_content, main_content_selector)

        return {
            "url": url,
            "title": title,
            "headings": extracted_data['headings'],
            "paragraphs": extracted_data['paragraphs'],
            "code_snippets": extracted_data['code_snippets']
        }
    except Exception as e:
        print(f"Error extracting data from {url}: {e}", file=sys.stderr)
        return None

def main():
    sitemap_url = "https://playwright.dev/sitemap.xml"
    output_path = "playwright_docs.json"
    sleep_time = 0.5

    all_urls = fetch_sitemap_urls(sitemap_url)
    if not all_urls:
        print("No URLs fetched from sitemap. Exiting.", file=sys.stderr)
        return

    testing_urls = filter_docs_urls(all_urls)
    if not testing_urls:
        print("No relevant URLs found after filtering. Exiting.", file=sys.stderr)
        return

    results = []
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        print("Playwright browser launched.")

        try:
            for i, url in enumerate(testing_urls):
                print(f"Scraping ({i+1}/{len(testing_urls)}): {url}")
                page = browser.new_page()
                entry = extract_documentation(page, url)
                if entry:
                    results.append(entry)
                page.close()
                time.sleep(sleep_time)
        except Exception as e:
            print(f"An error occurred with the Playwright browser: {e}", file=sys.stderr)
        finally:
            browser.close()
            print("Playwright browser closed.")

    if results:
        print(f"Saving {len(results)} extracted documents to {output_path}...")
        try:
            with open(output_path, "w", encoding='utf-8') as f:
                json.dump(results, f, indent=2)
            print(f"Documentation successfully saved to {output_path}")
        except IOError as e:
            print(f"Error saving results to {output_path}: {e}", file=sys.stderr)
    else:
        print("No documentation was successfully extracted to save.", file=sys.stderr)

if __name__ == "__main__":
    main()