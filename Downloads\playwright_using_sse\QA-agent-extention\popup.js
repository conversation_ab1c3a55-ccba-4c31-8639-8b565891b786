document.getElementById("send").addEventListener("click", () => {
  const status = document.getElementById("status");
  status.textContent = "Sending URL...";
  
  chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
    const tabUrl = tabs[0].url;
    const streamlitUrl = `http://localhost:8501?url=${encodeURIComponent(tabUrl)}`;
    
    chrome.tabs.create({ url: streamlitUrl }, () => {
      status.textContent = "Streamlit app opened in new tab!";
    });
  });
});