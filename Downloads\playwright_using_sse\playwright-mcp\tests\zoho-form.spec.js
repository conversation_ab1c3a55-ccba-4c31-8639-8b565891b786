const { test, expect } = require('@playwright/test');

test('submit zoho form with realistic values', async ({ page }) => {
  // Navigate to the form
  await page.goto('https://forms.zohopublic.in/rizwinka050gm1/form/Signup/formperma/V46jGONaXRKuRnuK9imm0KDy_L7jh03VjWeNjukrVx8');
  await page.waitForTimeout(2000); // Wait for page to load

  // Fill out the form
  await page.getByRole('textbox', { name: 'Name First' }).fill('John');
  await page.waitForTimeout(1000);

  await page.getByRole('textbox', { name: 'Name Last' }).fill('Smith');
  await page.waitForTimeout(1000);

  await page.getByRole('textbox', { name: 'Phone Required' }).fill('9876543210');
  await page.waitForTimeout(1000);

  await page.getByRole('textbox', { name: 'Email Required' }).fill('<EMAIL>');
  await page.waitForTimeout(1000);

  await page.getByRole('textbox', { name: 'company name' }).fill('TechCorp Solutions');
  await page.waitForTimeout(1000);

  // Submit the form
  await page.getByRole('button', { name: 'Sign me up' }).click();
  await page.waitForTimeout(2000);

  // Verify submission was successful
  await expect(page).toHaveURL(/.*thankyou/); // Check URL contains "thankyou"
  await expect(page.getByText('Thank you! Your response has been submitted.')).toBeVisible();
});
