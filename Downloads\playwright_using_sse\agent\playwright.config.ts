import { defineConfig } from '@playwright/test';

import type { Project } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [['html', { open: 'always' }]],
  use: {
    screenshot: 'on', // Add this to capture screenshots on test failure
  },
  projects: [
    { name: 'chrome' },
    // { name: 'msedge', use: { mcpBrowser: 'msedge' } },
    // { name: 'chromium', use: { mcpBrowser: 'chromium' } },
    // { name: 'firefox', use: { mcpBrowser: 'firefox' } },
    // { name: 'webkit', use: { mcpBrowser: 'webkit' } },
  ].filter(Boolean) as Project[],
});
