Test URL - https://emlabsform.onrender.com/

Extracted Data:
Form Validation Rules 
Field Order Specification 
Fields must appear in the following order: 
First Name 
Last Name 
Date of Birth 
Phone Number 
Email 
Website 
Field-Specific Validation Rules 
Phone Number 
Status: Mandatory 
Format: Must be digits, avoid country code 
Example: 123457891
Character Restrictions: No letters, symbols, or spaces permitted 
Validation Timing:  
For invalid characters and format: Only validated upon form submission 
Error Messages:  
Empty field: "Enter a number for this field." 
Non-numeric characters: "Enter only numbers." 
Email 
Status: Mandatory 
Format: Must follow standard email format (<EMAIL>) 
Character Restrictions: No spaces allowed 
Requirements: Must contain an @ symbol and valid domain 
Behavior: Leading/trailing spaces are automatically trimmed 
Error Message: "Enter a valid email address. (eg: <EMAIL>)" 
Date of Birth 
Status: Mandatory 
Format: dd-MMM-yyyy (e.g., 25-Apr-2007) , No other date format is accepted
Age Requirement: User must be at least 18 years old 
Date Restrictions: Future dates not accepted 
Error Message:” Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18." 
First Name and Last Name 
Status: Optional 
Character Restrictions:  
Only letters allowed (A-Z, a-z) 
No numbers, symbols, emojis, special characters (including hyphens) 
No accented letters (e.g., "<PERSON>") 
No spaces within or between names 
Behavior: Leading/trailing spaces are automatically trimmed 
Error Message:
First Name - "First name must contain alphabetic characters only." 
Last Name - “Last name must contain alphabetic characters only.”
Website 
Status: Optional 
Format Requirements:  
Must start with www.
No spaces allowed 
URLs starting with http:// or https:// without www. are invalid 
Error Message: "Enter a valid website. (eg: www.domain.com)" 
Empty Field: No error displayed if left empty 
Acceptance Criteria 
Happy Path Scenarios 
Form submission succeeds with only mandatory fields (Phone Number, Email, Date of Birth) correctly filled 
Form submission succeeds with all fields correctly filled 
First Name and Last Name can be empty without blocking submission 
Mixed case names (e.g., "JohnDOE") are accepted 
Date of Birth exactly 18 years before current date is accepted 
Subdomain emails (e.g., <EMAIL>) are accepted 
Negative Path Scenarios 
Phone Number:  
Empty field → Error: "Enter a number for this field." 
Contains letters/symbols → Error displayed 
Email:  
Empty field → Error: "Enter a valid email address. (eg: <EMAIL>)" 
Missing @ symbol or domain → Error displayed 
Contains whitespace → Error displayed 
Date of Birth:  
Unable to select future date
Only the date corresponding to today minus 18 years is visible — no other dates, such as today minus 10 years, will be shown.
Name Fields:  
Contains numbers/symbols/spaces → Error:
First Name - "First name must contain alphabetic characters only." 
Last Name - “Last name must contain alphabetic characters only.”
Website Field:  
Invalid format (missing www. or domain) → Error: "Enter a valid website. (eg: www.domain.com)" 
Empty field → No error displayed 
Multiple Errors:  
All required fields empty → Multiple relevant error messages displayed 
Fields with only spaces → Relevant error messages triggered on submit 
Edge Case Scenarios 
Names with hyphens or accented characters → Error: "Only letters are allowed." 
Subdomain emails (<EMAIL>) → Accepted 
Date of Birth exactly 18 years ago → Accepted 
Date of Birth one day short of 18 years → no date will be entered, error message “Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.”
Website starts with protocol (http://) but not www. → Rejected 
Website starts with www. but missing domain extension → Rejected 
Form submission succeeds with only Phone Number, Email, and valid Date of Birth filled 
Leading/trailing spaces names will trigger error message
No messages will be displayed after submission of the form
No error message  on phone number length
Validate for different ages 20,50,75  → Accepted