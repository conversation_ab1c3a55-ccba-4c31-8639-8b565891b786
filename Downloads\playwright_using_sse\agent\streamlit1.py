import streamlit as st
import asyncio
from dotenv import load_dotenv
import os

from langchain_groq import ChatGroq
from mcp_use import MCPAgent, MCPClient

import threading
import requests

N8N_WEBHOOK_URL = "http://localhost:5678/webhook/streamlit-to-n8n"

# Load environment variables
load_dotenv()
os.environ["GROQ_API_KEY"] = os.getenv("GROQ_API_KEY")
config_file = "agent/config.json"

system_prompt = (
    "You are an intelligent QA testing assistant with access to the web and capable of generating Playwright test automation using MCP tools. "
    "When the user provides a URL and specifies any input field to validate (e.g., 'email', 'password', 'first name', etc.), follow these steps carefully:\n\n"
    "1. Navigate to the given URL.\n"
    "2. Identify and locate the specified input field based on its label, placeholder, or surrounding context.\n"
    "3. Perform a **comprehensive set of validation tests** on the field, including but not limited to:\n"
    "   - Empty input\n"
    "   - Valid input (according to typical expectations for the field)\n"
    "   - Invalid formats (e.g., numbers in a name, bad emails, short/long passwords, etc.)\n"
    "   - Special characters\n"
    "   - Overly long input (50+ characters)\n"
    "   - Edge cases specific to that field type\n"
    "4. Submit the form after each test input and observe any validation or error messages specific to the field.\n"
    "5. Collect and summarize the results of each validation test clearly, indicating whether the input was accepted or rejected and the exact message shown.\n"
    "6. Once all tests are complete, generate and return a **single, complete Playwright script** that includes all the test cases sequentially.\n"
    "7. The code should be clean, well-structured, and reflect the actual actions taken during validation.\n\n"
    "Do not generate code until all validations have been tested using the tools available to you. Focus on accuracy and completeness of both results and code."
)

# Initialize session state
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []

if "agent" not in st.session_state:
    client = MCPClient.from_config_file(config_file)
    llm = ChatGroq(model="qwen-qwq-32b")
    st.session_state.agent = MCPAgent(
        llm=llm,
        client=client,
        max_steps=50,
        memory_enabled=True,
        system_prompt=system_prompt
    )

# Title
st.title("🤖 Interactive MCP Chat")

# Input field
user_input = st.text_input("You:", key="user_input", label_visibility="visible")

# Send message button
if st.button("Send") and user_input:
    async def handle_and_run():
        try:
            # Send prompt to n8n and wait for refined response
            with st.spinner("Refining your prompt via n8n..."):
                response = requests.post(N8N_WEBHOOK_URL, json={"initial_prompt": user_input})
                print(response.json())
                
                if response.status_code == 200:
                    json_data = response.json()
                    refined_prompt = json_data[0].get("output")
                    
                    st.session_state.chat_history.append(("You", user_input))
                    st.session_state.chat_history.append(("Assistant", f"🧠 Refined Prompt:\n\n```{refined_prompt}```"))
                    # Run refined prompt via MCP agent
                    print(type(refined_prompt))
                    final_response = await st.session_state.agent.run(refined_prompt)
                    st.session_state.chat_history.append(("Assistant", final_response))
                else:
                    st.session_state.chat_history.append(("Error", f"❌ n8n returned status {response.status_code}: {response.text}"))
        except Exception as e:
            st.session_state.chat_history.append(("Error", str(e)))

    asyncio.run(handle_and_run())
    st.rerun()

# Display chat history
for role, message in st.session_state.chat_history:
    if role == "You":
        st.markdown(f"🧑 **{role}:** {message}")
    elif role == "Assistant":
        st.markdown(f"🤖 **{role}:** {message}")
    else:
        st.error(f"{role}: {message}")

# Clear conversation history
if st.button("Clear History"):
    st.session_state.agent.clear_conversation_history()
    st.session_state.chat_history = []
    st.rerun()
