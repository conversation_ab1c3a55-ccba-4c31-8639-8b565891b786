import { test, expect } from '@playwright/test';

test.describe('Name Validation for Leading/Trailing Spaces', () => {
  const FORM_URL = 'https://emlabsform.onrender.com/';
  const FIRST_NAME_ERROR = 'First name must contain alphabetic characters only.';
  const LAST_NAME_ERROR = 'Last name must contain alphabetic characters only.';

  test('should trim spaces and show alphabetic-only errors for names', async ({ page }) => {
    // Step 1: Navigate to page
    try {
      await page.goto(FORM_URL, { waitUntil: 'load', timeout: 10_000 });
    } catch {
      throw new Error('Page failed to load');
    }
    await expect(page).toHaveURL(FORM_URL, { timeout: 10_000 });

    // Helper: wait for a field and fill value if provided
    const fillField = async (
      label: string,
      value: string | null,
      missingMsg: string
    ) => {
      const locator = page.getByLabel(label);
      try {
        await locator.waitFor({ state: 'visible', timeout: 5_000 });
      } catch {
        throw new Error(missingMsg);
      }
      if (value !== null) {
        // include leading/trailing spaces as part of input
        await locator.fill(value);
      }
    };

    // Step 2: First Name (optional) – enter with spaces
    await fillField('First Name', ' John ', 'First Name field missing');

    // Step 3: Last Name (optional) – enter with spaces
    await fillField('Last Name', ' Smith ', 'Last Name field missing');

    // Step 4: Phone Number (mandatory)
    await fillField('Phone Number', '123456789', 'Phone Number field missing');

    // Step 5: Email (mandatory)
    await fillField('Email', '<EMAIL>', 'Email field missing');

    // Step 6: Date of Birth (mandatory)
    await fillField('Date of Birth', '15-May-2007', 'Date of Birth field missing');

    // Step 7: Website (optional)
    await fillField('Website', 'www.domain.com', 'Website field missing');

    // Step 8: Click Submit
    const submitBtn = page.getByRole('button', { name: 'Submit' });
    try {
      await submitBtn.waitFor({ state: 'visible', timeout: 10_000 });
    } catch {
      throw new Error('Submit button missing');
    }
    await submitBtn.click();

    // Step 9: Verify First Name error
    const firstNameError = page.getByText(FIRST_NAME_ERROR);
    try {
      await firstNameError.waitFor({ state: 'visible', timeout: 10_000 });
    } catch {
      throw new Error('First Name error message not displayed');
    }
    await expect(firstNameError).toBeVisible();

    // Step 10: Verify Last Name error
    const lastNameError = page.getByText(LAST_NAME_ERROR);
    try {
      await lastNameError.waitFor({ state: 'visible', timeout: 10_000 });
    } catch {
      throw new Error('Last Name error message not displayed');
    }
    await expect(lastNameError).toBeVisible();
  });
});