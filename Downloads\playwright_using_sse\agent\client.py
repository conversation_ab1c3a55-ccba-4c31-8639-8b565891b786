import asyncio

from dotenv import load_dotenv
from langchain_groq import <PERSON>tGro<PERSON>

from mcp_use import MCPAgent, MCPClient
import os

async def run_memory_chat():
    """Run a chat using MCPAgent's built-in conversation memory."""
    # Load environment variables for API keys
    load_dotenv()
    os.environ["GROQ_API_KEY"]=os.getenv("GROQ_API_KEY")

    # Config file path - change this to your config file
    config_file = "agent/config.json"

    print("Initializing chat...")

    system_prompt = (
    "You are an intelligent QA testing assistant equipped with web automation capabilities using Playwright via MCP. "
    "When a user provides a URL and specifies a field to validate (e.g., 'first name'), follow these steps: \n"
    "1. Navigate to the given URL. \n"
    "2. Locate the specified field and perform a full set of validation tests (e.g., empty input, overly long input, numbers, special characters, valid input, etc.). \n"
    "3. Submit the form after each test input and observe any error or validation messages related to that field. \n"
    "4. Log or summarize the outcomes of each validation test, including any error messages or UI behavior. \n"
    "5. After executing the tests, display the Playwright code that was used to perform these validations. \n"
    "Use the tools available to you via MCP and act only after running the tests in the environment. "
)

    # Create MCP client and agent with memory enabled
    client = MCPClient.from_config_file(config_file)
    llm = ChatGroq(model="qwen-qwq-32b")

    # Create agent with memory_enabled=True
    agent = MCPAgent(
        llm=llm,
        client=client,
        max_steps=30,
        memory_enabled=True,
        system_prompt=system_prompt,  # Enable built-in conversation memory
    )

    print("\n===== Interactive MCP Chat =====")
    print("Type 'exit' or 'quit' to end the conversation")
    print("Type 'clear' to clear conversation history")
    print("==================================\n")

    try:
        # Main chat loop
        while True:
            # Get user input
            user_input = input("\nYou: ")

            # Check for exit command
            if user_input.lower() in ["exit", "quit"]:
                print("Ending conversation...")
                break

            # Check for clear history command
            if user_input.lower() == "clear":
                agent.clear_conversation_history()
                print("Conversation history cleared.")
                continue

            # Get response from agent
            print("\nAssistant: ", end="", flush=True)

            try:
                # Run the agent with the user input (memory handling is automatic)
                response = await agent.run(user_input)
                print(response)

            except Exception as e:
                print(f"\nError: {e}")

    finally:
        # Clean up
        if client and client.sessions:
            await client.close_all_sessions()


if __name__ == "__main__":
    asyncio.run(run_memory_chat())