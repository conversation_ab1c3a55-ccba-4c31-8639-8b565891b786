No,Test Description,Detailed Test Steps
TC001,Happy path with only mandatory fields correctly filled,"**

1. Navigate to page ""https://form2-4y5z.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Page failed to load"".

2. Verify field ""First Name"" is optional; leave blank; if field is not found, fail with message ""First Name field missing"".

3. Verify field ""Last Name"" is optional; leave blank; if field is not found, fail with message ""Last Name field missing"".

4. Enter ""123456789"" in field ""Phone Number""; wait up to 5 seconds for field to be available; if field is not found, fail with message ""Phone Number field missing"".

5. Enter ""<EMAIL>"" in field ""Email""; wait up to 5 seconds for field to be available; if field is not found, fail with message ""Email field missing"".

6. Verify field ""Website"" is optional; leave blank; if field is not found, fail with message ""Website field missing"".

7. Select ""Male"" in field ""Gender""; wait up to 5 seconds for field to be available; if field is not found, fail with message ""Gender field missing"".

8. Click button ""Submit""; wait up to 5 seconds for button to be available; if button is not found, fail with message ""Submit button missing"".

9. Verify no text containing ""error"" is visible; wait up to 10 seconds for any error messages to appear; if error text is found, fail with message ""Error message detected after form submission""."
