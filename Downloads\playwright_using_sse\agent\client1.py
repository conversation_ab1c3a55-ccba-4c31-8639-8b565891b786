import streamlit as st
import asyncio
from dotenv import load_dotenv
import os

from langchain_groq import ChatGro<PERSON>
from mcp_use import MCPAgent, MCPClient

# Load environment variables
load_dotenv()
os.environ["GROQ_API_KEY"] = os.getenv("GROQ_API_KEY")
config_file = "agent/config.json"

# Initialize session state
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []

if "agent" not in st.session_state:
    client = MCPClient.from_config_file(config_file)
    llm = ChatGroq(model="qwen-qwq-32b")
    st.session_state.agent = MCPAgent(
        llm=llm,
        client=client,
        max_steps=15,
        memory_enabled=True
    )

# Title
st.title("🤖 Interactive MCP Chat")

# Input field
user_input = st.text_input("You:", key="user_input", label_visibility="visible")

# Send message button
if st.button("Send") and user_input:
    async def handle_input():
        try:
            response = await st.session_state.agent.run(user_input)
            st.session_state.chat_history.append(("You", user_input))
            st.session_state.chat_history.append(("Assistant", response))
        except Exception as e:
            st.session_state.chat_history.append(("Error", str(e)))

    asyncio.run(handle_input())
    st.rerun()  # Clears input after submission

# Display chat history
for role, message in st.session_state.chat_history:
    if role == "You":
        st.markdown(f"🧑 **{role}:** {message}")
    elif role == "Assistant":
        st.markdown(f"🤖 **{role}:** {message}")
    else:
        st.error(f"{role}: {message}")

# Clear conversation history
if st.button("Clear History"):
    st.session_state.agent.clear_conversation_history()
    st.session_state.chat_history = []
    st.rerun()
