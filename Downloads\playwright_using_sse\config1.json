{"browser": {"browserName": "chromium", "isolated": false, "userDataDir": "./userdata", "launchOptions": {"channel": "chrome", "headless": false, "executablePath": ""}, "contextOptions": {"viewport": {"width": 1280, "height": 720}}, "cdpEndpoint": "", "remoteEndpoint": ""}, "server": {"port": 3000, "host": "localhost", "mcpServer": {"url": "http://localhost:8931/sse", "headers": {"Authorization": ""}}}, "capabilities": ["core", "tabs", "pdf", "history", "wait", "files", "install", "testing"], "vision": true, "outputDir": "./output", "network": {"allowedOrigins": ["*"], "blockedOrigins": []}, "noImageResponses": false}