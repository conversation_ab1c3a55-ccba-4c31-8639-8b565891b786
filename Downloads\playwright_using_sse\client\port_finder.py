import socket
import subprocess
import urllib.request
import urllib.error
import time
import os
import time
import os
import sys
import psutil
def is_port_in_use_socket(port):
    """
    Check if a port is in use using socket connection.
    """
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) == 0

def is_port_in_use_urllib(port):
    """
    Check if a port is in use by trying to access a URL.
    """
    try:
        urllib.request.urlopen(f"http://localhost:{port}", timeout=1)
        return True
    except (urllib.error.URLError, ConnectionRefusedError):
        return False

def is_port_in_use_command(port):
    """
    Check if a port is in use using system commands.
    """
    try:
        if os.name == 'nt':  # Windows
            output = subprocess.check_output(f"netstat -ano | findstr :{port} | findstr LISTENING", shell=True)
            return len(output) > 0
        else:  # Linux/Mac
            output = subprocess.check_output(f"lsof -i :{port}", shell=True)
            return len(output) > 0
    except subprocess.CalledProcessError:
        return False

def is_port_in_use(port):
    """
    Check if a port is in use using multiple methods for reliability.
    """
    # Try multiple methods in case one fails
    methods = [is_port_in_use_socket, is_port_in_use_urllib, is_port_in_use_command]
    
    for method in methods:
        try:
            if method(port):
                print(f"Port {port} is in use (detected by {method.__name__})")
                return True
        except Exception as e:
            print(f"Error checking port with {method.__name__}: {e}")
    
    print(f"Port {port} is not in use (all methods checked)")
    return False

def find_available_port(start_port=9323):
    """
    Find the next available port.
    If start_port is in use, increment until finding an open port.
    """
    port = start_port
    max_port = start_port + 10  # Don't check indefinitely
    
    while port < max_port:
        if is_port_in_use(port):
            print(f"Port {port} is in use, trying port {port+1}")
            port += 1
        else:
            print(f"Port {port} is available and will be used")
            return port
    
    # If we get here, just return the next port after start_port
    return start_port + 1


def get_ports_starting_with(prefix):
    """
    Get a comprehensive list of ports starting with the specified prefix.
    Uses multiple methods to ensure all ports are found.
    
    :param prefix: Prefix of the port number (e.g., '9')
    :return: List of ports currently in use that start with the prefix
    """
    in_use_ports = set()  # Use a set to avoid duplicates
    
    # Method 1: Check using psutil network connections
    try:
        connections = psutil.net_connections(kind='all')
        for conn in connections:
            # Check both local and remote addresses if they exist
            if hasattr(conn, 'laddr') and conn.laddr:
                port = conn.laddr.port
                if str(port).startswith(str(prefix)):
                    in_use_ports.add(port)
            if hasattr(conn, 'raddr') and conn.raddr:
                port = conn.raddr.port
                if str(port).startswith(str(prefix)):
                    in_use_ports.add(port)
    except Exception as e:
        print(f"Error with psutil connections check: {e}")

    # Method 2: Use system commands for thoroughness
    try:
        if os.name == 'nt':  # Windows
            cmd = f"netstat -ano | findstr /C:\":9\""
            output = subprocess.check_output(cmd, shell=True, text=True)
            lines = output.strip().split('\n')
            for line in lines:
                parts = line.split()
                if len(parts) >= 2:
                    addr_port = parts[1].split(':')
                    if len(addr_port) == 2 and addr_port[1].startswith(str(prefix)):
                        try:
                            in_use_ports.add(int(addr_port[1]))
                        except ValueError:
                            continue
        else:  # Linux/Mac
            cmd = f"lsof -i | grep ':{prefix}'"
            try:
                output = subprocess.check_output(cmd, shell=True, text=True)
                lines = output.strip().split('\n')
                for line in lines:
                    parts = line.split()
                    if len(parts) >= 9:
                        addr_port = parts[8].split(':')
                        if len(addr_port) >= 2:
                            port_str = addr_port[-1]
                            # Handle cases like "9323 (LISTEN)"
                            port_str = port_str.split()[0].strip('()')
                            if port_str.startswith(str(prefix)):
                                try:
                                    in_use_ports.add(int(port_str))
                                except ValueError:
                                    continue
            except subprocess.CalledProcessError:
                # This might happen if no matching ports are found
                pass
    except Exception as e:
        print(f"Error with system command check: {e}")

    # Method 3: Check each port in the range directly
    start_port = int(str(prefix) + "000")
    end_port = int(str(prefix) + "999")
    
    # Only scan a reasonable range to avoid extreme delays
    scan_range = min(100, end_port - start_port)
    
    for port in range(start_port, start_port + scan_range):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(0.1)  # Short timeout for quick scanning
                result = s.connect_ex(('localhost', port))
                if result == 0:  # Port is open
                    in_use_ports.add(port)
        except:
            pass
    
    # Convert set to sorted list
    result = sorted(list(in_use_ports))
    
    print(f"Ports currently in use starting with {prefix}:")
    for port in result:
        print(f"Port {port}")
    
    return result


def get_process_by_port(port):
    """
    Find processes using the specified port.
    
    :param port: Port number to check
    :return: List of process information
    """
    found_processes = []
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                # Check if the process is listening on the port
                for conn in proc.connections():
                    if conn.status == psutil.CONN_LISTEN and conn.laddr.port == port:
                        found_processes.append({
                            'pid': proc.pid,
                            'name': proc.name(),
                            'cmdline': ' '.join(proc.cmdline()) if proc.cmdline() else ''
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
    except Exception as e:
        print(f"Error finding processes: {e}")
    
    return found_processes

def wait_and_show_countdown(wait_time):
    """
    Wait for a specified time and show a countdown.
    
    :param wait_time: Time to wait in seconds
    """
    print(f"\nWaiting {wait_time} seconds before terminating processes...")
    for remaining in range(wait_time, 0, -1):
        sys.stdout.write(f"\rTime remaining: {remaining} seconds")
        sys.stdout.flush()
        time.sleep(1)
    print("\nProceeding with process termination...")
    
def close_port_processes(ports):
    """
    Close processes using the specified ports.
    
    :param ports: List of ports to close
    :return: Number of processes terminated
    """
    terminated_count = 0
     # Wait 60 seconds before terminating
    wait_and_show_countdown(60)
    # Iterate through specified ports
    for port in ports:
        processes = get_process_by_port(port)
        
        if not processes:
            print(f"No processes found using port {port}")
            continue
        
        print(f"\nProcesses using port {port}:")
        for proc in processes:
            print(f"PID: {proc['pid']}, Name: {proc['name']}, Command: {proc['cmdline']}")
        
        # Attempt to terminate processes
        for proc in processes:
            try:
                process = psutil.Process(proc['pid'])
                print(f"Terminating process PID {proc['pid']} ({proc['name']})")
                process.terminate()
                terminated_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                print(f"Could not terminate process {proc['pid']}: {e}")
    
    return terminated_count

def main_function_call():

    """
    Main function to call when running this script directly. It will attempt to close any processes
    using ports starting with 9 and wait a moment for the ports to be released.

    :return: None
    """


    try:
        # Get list of ports starting with 9
        used_ports = get_ports_starting_with(9)
        
        if not used_ports:
            print("No ports starting with 9 are currently in use.")
            return
        
        # Close processes for used ports
        terminated_count = close_port_processes(used_ports)
        print(f"\nTerminated {terminated_count} process(es)")
        
        # Wait a moment for ports to be released
        time.sleep(2)
        
        
    
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
