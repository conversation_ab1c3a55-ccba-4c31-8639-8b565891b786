Test_Case_ID,Test_Case_Description,First_Name,Last_Name,Date_of_Birth,Phone_Number,Email,Website,Expected_Result,Error_Message,Test_Case_Category,Test_Steps,Test_Steps_AI
TC005,Edge Case: Date of Birth one day short of 18 years is rejected with error.,<PERSON>,<PERSON>,26-Jun-06,1.23E+12,<EMAIL>,"""",Failure,Enter a valid date.,<PERSON>,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): enter 'John'. 3. Last Name (optional): enter '<PERSON>'. 4. Date of Birth (mandatory): enter '26-Jun-2006' (one day less than 18 years). 5. Phone Number (mandatory): enter '1234567890123'. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: form submission blocked with error message 'Enter a valid date.' for Date of Birth.,"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Page failed to load"".

2. Verify field labeled ""First Name"" is optional; enter ""John"" in field labeled ""First Name""; wait up to 5 seconds for field to accept input; if field is not found, fail with message ""First Name field missing"".

3. Verify field labeled ""Last Name"" is optional; enter ""Smith"" in field labeled ""Last Name""; wait up to 5 seconds for field to accept input; if field is not found, fail with message ""Last Name field missing"".

4. Verify field labeled ""Date of Birth"" is mandatory; enter ""10-May-2007"" in field labeled ""Date of Birth""; wait up to 5 seconds for field to accept input; if field is not found, fail with message ""Date of Birth field missing"".

5. Verify field labeled ""Phone Number"" is mandatory; enter ""1234567890123"" in field labeled ""Phone Number""; wait up to 5 seconds for field to accept input; if field is not found, fail with message ""Phone Number field missing"".

6. Verify field labeled ""Email"" is mandatory; enter ""<EMAIL>"" in field labeled ""Email""; wait up to 5 seconds for field to accept input; if field is not found, fail with message ""Email field missing"".

7. Verify field labeled ""Website"" is optional; leave field labeled ""Website"" empty; if field is not found, fail with message ""Website field missing"".

8. Click button labeled ""Submit""; wait up to 10 seconds for form submission response; if button is not found, fail with message ""Submit button missing"".

9. Verify text ""Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18."" is visible; wait up to 10 seconds for error message to appear; if text is not found, fail with message ""Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.""."
TC006,Negative Path: Phone Number empty field triggers error.,John,Smith,25-Jun-06,"""",<EMAIL>,"""",Failure,Enter a number for this field.,Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): enter 'John'. 3. Last Name (optional): enter 'Smith'. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): leave empty. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: error message 'Enter a number for this field.' displayed for Phone Number.,"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Page failed to load"".

2. Enter ""John"" in field labeled ""First Name (optional)""; wait up to 5 seconds for field to be available; if field is not found, fail with message ""First Name field missing"".

3. Enter ""Smith"" in field labeled ""Last Name (optional)""; wait up to 5 seconds for field to be available; if field is not found, fail with message ""Last Name field missing"".

4. Enter ""25-Jun-2006"" in field labeled ""Date of Birth (mandatory)""; wait up to 5 seconds for field to be available; if field is not found, fail with message ""Date of Birth field missing"".

5. Verify field labeled ""Phone Number (mandatory)"" is empty; wait up to 5 seconds for field to be available; if field is not found, fail with message ""Phone Number field missing"".

6. Enter ""<EMAIL>"" in field labeled ""Email (mandatory)""; wait up to 5 seconds for field to be available; if field is not found, fail with message ""Email field missing"".

7. Verify field labeled ""Website (optional)"" is empty; wait up to 5 seconds for field to be available; if field is not found, fail with message ""Website field missing"".

8. Click button ""Submit""; wait up to 10 seconds for form submission to complete; if button is not found, fail with message ""Submit button missing"".

9. Verify text ""Enter a number for this field."" is visible near field labeled ""Phone Number (mandatory)""; wait up to 10 seconds for error message to appear; if message is not found, fail with message ""Phone Number error message not displayed""."
TC003,Negative Path: Phone Number field left empty triggers error.,"""","""",25-Jun-06,"""",<EMAIL>,"""",Failure,Enter a number for this field.,Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): leave empty. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: Error message 'Enter a number for this field.' displayed for Phone Number.,"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Page failed to load"".

2. Verify field labeled ""First Name"" is present; wait up to 5 seconds for element to be focusable; leave field empty; if not found, fail with message ""First Name field not found"".

3. Verify field labeled ""Last Name"" is present; wait up to 5 seconds for element to be focusable; leave field empty; if not found, fail with message ""Last Name field not found"".

4. Enter ""25-Jun-2006"" in field labeled ""Date of Birth""; wait up to 5 seconds for element to be focusable; verify input is set; if not found, fail with message ""Date of Birth field not found"".

5. Verify field labeled ""Phone Number"" is present; wait up to 5 seconds for element to be focusable; leave field empty; if not found, fail with message ""Phone Number field not found"".

6. Enter ""<EMAIL>"" in field labeled ""Email""; wait up to 5 seconds for element to be focusable; verify input is set; if not found, fail with message ""Email field not found"".

7. Verify field labeled ""Website"" is present; wait up to 5 seconds for element to be focusable; leave field empty; if not found, fail with message ""Website field not found"".

8. Click button ""Submit""; wait up to 5 seconds for element to be focusable; verify button is clicked; if not found, fail with message ""Submit button not found"".

9. Verify text ""Enter a number for this field."" is visible; wait up to 10 seconds for message to appear; if not found, fail with message ""Error message not displayed for Phone Number""."
TC003,Negative Path: Phone Number field left empty triggers error.,"""","""",25-Jun-06,"""",<EMAIL>,"""",Failure,Enter a number for this field.,Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): leave empty. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: Error message 'Enter a number for this field.' displayed for Phone Number.,"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Page failed to load"".

2. Verify field labeled ""First Name"" is present; wait up to 5 seconds for element to be focusable; leave field empty; if not found, fail with message ""First Name field not found"".

3. Verify field labeled ""Last Name"" is present; wait up to 5 seconds for element to be focusable; leave field empty; if not found, fail with message ""Last Name field not found"".

4. Enter ""25-Jun-2006"" in field labeled ""Date of Birth""; wait up to 5 seconds for element to be focusable; verify input is set; if not found, fail with message ""Date of Birth field not found"".

5. Verify field labeled ""Phone Number"" is present; wait up to 5 seconds for element to be focusable; leave field empty; if not found, fail with message ""Phone Number field not found"".

6. Enter ""<EMAIL>"" in field labeled ""Email""; wait up to 5 seconds for element to be focusable; verify input is set; if not found, fail with message ""Email field not found"".

7. Verify field labeled ""Website"" is present; wait up to 5 seconds for element to be focusable; leave field empty; if not found, fail with message ""Website field not found"".

8. Click button ""Submit""; wait up to 5 seconds for element to be focusable; verify button is clicked; if not found, fail with message ""Submit button not found"".

9. Verify text ""Enter a number for this field."" is visible; wait up to 10 seconds for message to appear; if not found, fail with message ""Error message not displayed for Phone Number""."
TC004,Negative Path: Phone Number contains letters triggers immediate error and submission error.,"""","""",25-Jun-06,12345abc6789,<EMAIL>,"""",Failure,Enter only numbers.,Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): enter '12345abc6789' (contains letters). 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Observe immediate error 'Enter only numbers.' during typing in Phone Number field. 9. Attempt to submit form. Expected: Submission blocked with error 'Enter only numbers.' for Phone Number.,"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Page failed to load"".

2. Verify field labeled ""First Name"" is empty; wait up to 5 seconds for element to be focusable; if not found, fail with message ""First Name field not found"".

3. Verify field labeled ""Last Name"" is empty; wait up to 5 seconds for element to be focusable; if not found, fail with message ""Last Name field not found"".

4. Enter ""25-Jun-2006"" in field labeled ""Date of Birth""; wait up to 5 seconds for element to be focusable; verify input is set; if not found, fail with message ""Date of Birth field not found"".

5. Enter ""12345abc6789"" in field labeled ""Phone Number""; wait up to 5 seconds for element to be focusable; verify input is set; if not found, fail with message ""Phone Number field not found"".

6. Verify text ""Enter only numbers."" is visible near ""Phone Number"" field; wait up to 5 seconds for message to appear; if not found, fail with message ""Phone Number error not displayed"".

7. Enter ""<EMAIL>"" in field labeled ""Email""; wait up to 5 seconds for element to be focusable; verify input is set; if not found, fail with message ""Email field not found"".

8. Verify field labeled ""Website"" is empty; wait up to 5 seconds for element to be focusable; if not found, fail with message ""Website field not found"".

9. Click button ""Submit""; wait up to 5 seconds for element to be focusable; verify button is clicked; if not found, fail with message ""Submit button not found"".

10. Verify text ""Enter only numbers."" is visible near ""Phone Number"" field; wait up to 10 seconds for error to appear; if not found, fail with message ""Phone Number error not displayed after submission""."
