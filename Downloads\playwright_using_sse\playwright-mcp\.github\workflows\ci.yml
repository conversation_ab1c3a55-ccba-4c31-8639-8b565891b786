name: CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  lint:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
    - name: Use Node.js 18
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    - name: Install dependencies
      run: npm ci
    - run: npm run build
    - name: Run ESLint
      run: npm run lint
    - run: npm run update-readme
    - name: Ensure no changes
      run: git diff --exit-code

  test:
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
    runs-on: ${{ matrix.os }}

    steps:
    - uses: actions/checkout@v4

    - name: Use Node.js 18
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Playwright install
      run: npx playwright install --with-deps

    - name: Install MS Edge
      if: ${{ matrix.os == 'macos-latest' }} # MS Edge is not preinstalled on macOS runners.
      run: npx playwright install msedge

    - name: Build
      run: npm run build

    - name: Install Playwright browsers
      run: npx playwright install --with-deps

    - name: Run tests
      run: npm test -- --forbid-only
