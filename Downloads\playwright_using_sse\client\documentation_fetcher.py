import asyncio
import os
import sys
import warnings
import concurrent.futures
import threading
import time
from dotenv import load_dotenv
from agno.agent import Agent
from agno.models.google import Gemini
from agno.tools.mcp import MCPTools

# --- Enhanced Windows asyncio fixes ---
if sys.platform == "win32":
    import asyncio.proactor_events
    
    # More comprehensive patching
    def safe_del(self):
        try:
            if hasattr(self, '_sock') and self._sock:
                self._sock = None
        except:
            pass
    
    def safe_close(self):
        try:
            if hasattr(self, '_sock') and self._sock:
                try:
                    self._sock.close()
                except:
                    pass
            if hasattr(self, '_transport') and self._transport:
                try:
                    self._transport.close()
                except:
                    pass
        except:
            pass
    
    def safe_repr(self):
        try:
            return f"<_ProactorBasePipeTransport>"
        except:
            return "<Transport>"
    
    # Apply patches
    asyncio.proactor_events._ProactorBasePipeTransport.__del__ = safe_del
    asyncio.proactor_events._ProactorBasePipeTransport.close = safe_close
    asyncio.proactor_events._ProactorBasePipeTransport.__repr__ = safe_repr
    
    # Set event loop policy
    asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # Suppress all async-related warnings
    warnings.filterwarnings("ignore", category=ResourceWarning)
    warnings.filterwarnings("ignore", message="coroutine.*was never awaited")
    warnings.filterwarnings("ignore", message="Task exception was never retrieved")

# Load environment variables
load_dotenv()

class AsyncContextManager:
    """Manages async context to prevent cleanup issues"""
    
    def __init__(self):
        self._loop = None
        self._executor = None
    
    def run_with_cleanup(self, coro, timeout=300):
        """Run coroutine with proper cleanup"""
        def _run_in_thread():
            # Create a new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                # Set up the loop
                self._loop = loop
                
                # Run the coroutine with timeout
                return loop.run_until_complete(asyncio.wait_for(coro, timeout=timeout))
                
            except asyncio.TimeoutError:
                return "Timeout: Operation took too long"
            except Exception as e:
                return f"Error: {str(e)}"
            finally:
                # Aggressive cleanup
                try:
                    # Cancel all remaining tasks
                    pending = asyncio.all_tasks(loop)
                    if pending:
                        for task in pending:
                            if not task.done():
                                task.cancel()
                        
                        # Give tasks a moment to cancel
                        try:
                            loop.run_until_complete(
                                asyncio.wait_for(
                                    asyncio.gather(*pending, return_exceptions=True),
                                    timeout=5
                                )
                            )
                        except:
                            pass
                    
                    # Close the loop
                    loop.close()
                    
                except Exception:
                    # If cleanup fails, just ignore
                    pass
                
                # Clear the loop reference
                self._loop = None
        
        # Run in a separate thread to isolate completely
        with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(_run_in_thread)
            return future.result(timeout=timeout + 30)

# Global context manager
_async_manager = AsyncContextManager()

async def _fetch_documentation_safe(test_step: str) -> str:
    """Safer async function with better error handling"""
    mcp_tools = None
    agent = None
    
    try:
        # Initialize MCP tools with shorter timeout
        mcp_tools = MCPTools("npx -y @upstash/context7-mcp@latest", timeout_seconds=60)
        await mcp_tools.__aenter__()
        
        # Create agent
        agent = Agent(
                    description="""
            You are a documentation fetcher for library id microsoft/playwright.

            Your role is to assist users by fetching and providing the most accurate, optimal, and up-to-date documentation using get-library-docs tool.

            The user will provide test steps or a feature document. Based on this input, you will:

            1. Analyze the user's goal and determine the relevant area of the Playwright documentation (TypeScript focus).
            2. Retrieve and present only documentation-based best practices from the official Microsoft Playwright documentation.

            Your response must follow this format:
            - Start with the heading: "Documentation to achieve optimal automation script:"
            - List only documentation-based best practices in clear, concise text.
            - Always use TypeScript code snippets.
            - Do not generate full test scripts 
            - Acts as a guide who provide advice to achieve optimal automation script.
            - Do not include any external links.
            - Do not add extra commentary or filler text.
            - Focus on text-based locators, assertions, and validations.
            - Emphasize official recommended approaches for interacting with elements, assertions, and validations.
            - Dont give documentation for steps/messages not mentioned explicitly
            - Dont invent field,names or values other than the ones mentioned in the test step
            - Do not include any additional information or context.
            - Do not include any information about the test step itself.
            - Structure the output in the order  of the test steps if specific test steps are provided.
            - Structure the output in a holistic way if feature specification is provided.
            - Always aim for optimal documentation-based solutions.
            - output complete code snippets for the test steps provided.

            Special focus:
            - For UI validation steps such as form validation, highlight best practices around locator strategies, assertion methods, and user interaction simulation.
            - Provide reasoning based solely on documented best practices.
            """,
                    model=Gemini(id="gemini-2.5-flash-preview-04-17", api_key=os.environ.get("GOOGLE_API_KEY")),
                    tools=[mcp_tools],
                    markdown=True,
                    instructions="Ensure the output serves as a guide for performing the test, rather than a complete test script.",
                )
        
        prompt = f"Find and return ONLY the relevant documentation for this test step: {test_step}"
        
        # Get response with timeout
        response_stream = await asyncio.wait_for(
            agent.arun(prompt, stream=True),
            timeout=120
        )
        
        output = []
        async for chunk in response_stream:
            if chunk is None:
                continue
            if hasattr(chunk, "content") and chunk.content:
                output.append(chunk.content)
            elif isinstance(chunk, str):
                output.append(chunk)
        
        result = "".join(output).strip()
        return result if result else "No documentation found"
        
    except asyncio.TimeoutError:
        return "Timeout: Documentation fetch took too long"
    except Exception as e:
        return f"Error: {str(e)}"
    finally:
        # Clean up MCP tools
        if mcp_tools:
            try:
                await mcp_tools.__aexit__(None, None, None)
            except:
                pass

def get_documentation(test_step: str) -> str:
    """
    Get relevant documentation for a test step.
    
    Args:
        test_step (str): The test step description from CSV
        
    Returns:
        str: Relevant documentation content only
    """
    if not test_step or not test_step.strip():
        return "Empty test step"
    
    try:
        return _async_manager.run_with_cleanup(
            _fetch_documentation_safe(test_step.strip()),
            timeout=180
        )
    except Exception as e:
        return f"Error: {str(e)}"

def process_csv_data(test_steps) -> list:
    """
    Process multiple test steps and return documentation for each.
    
    Args:
        test_steps: List of test step descriptions or single string
        
    Returns:
        list: List of documentation strings corresponding to each test step
    """
    # Handle single string input
    if isinstance(test_steps, str):
        test_steps = [test_steps]
    
    results = []
    for i, step in enumerate(test_steps):
        print(f"Processing step {i+1}/{len(test_steps)}: {step[:50]}...")
        doc = get_documentation(step)
        results.append(doc)
        # Small delay to prevent overwhelming the service
        time.sleep(1)
    
    return results
