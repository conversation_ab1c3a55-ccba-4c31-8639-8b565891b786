import { test, expect } from '@playwright/test';

test.describe('Date of Birth Validation – Underage User', () => {
  const FORM_URL = 'https://emlabsform.onrender.com/';
  const DOB_ERROR_TEXT =
    "Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.";

  test('should show DOB validation error for underage date', async ({ page }) => {
    // Step 1: Navigate to page
    try {
      await page.goto(FORM_URL, { waitUntil: 'load', timeout: 10_000 });
    } catch {
      throw new Error('Page failed to load');
    }
    await expect(page).toHaveURL(FORM_URL, { timeout: 10_000 });

    // Utility: wait for field and optionally fill
    const prepareField = async (
      label: string,
      missingMsg: string,
      fillValue?: string
    ) => {
      const locator = page.getByLabel(label);
      try {
        await locator.waitFor({ state: 'visible', timeout: 5_000 });
      } catch {
        throw new Error(missingMsg);
      }
      if (fillValue !== undefined) {
        await locator.fill(fillValue);
      }
    };

    // Step 2: First Name (optional, leave blank)
    await prepareField('First Name', 'First Name field missing');

    // Step 3: Last Name (optional, leave blank)
    await prepareField('Last Name', 'Last Name field missing');

    // Step 4: Phone Number (mandatory)
    await prepareField('Phone Number', 'Phone Number field missing', '123456789');

    // Step 5: Email (mandatory)
    await prepareField('Email', 'Email field missing', '<EMAIL>');

    // Step 6: Date of Birth (mandatory)
    await prepareField(
      'Date of Birth',
      'Date of Birth field missing',
      '16-May-2007'
    );

    // Step 7: Website (optional, leave blank)
    await prepareField('Website', 'Website field missing');

    // Step 8: Click Submit
    const submit = page.getByRole('button', { name: 'Submit' });
    try {
      await submit.waitFor({ state: 'visible', timeout: 10_000 });
    } catch {
      throw new Error('Submit button missing');
    }
    await submit.click();

    // Step 9: Verify DOB error message
    const dobError = page.getByText(DOB_ERROR_TEXT);
    try {
      await dobError.waitFor({ state: 'visible', timeout: 10_000 });
    } catch {
      throw new Error('Date of Birth error message not displayed');
    }
    await expect(dobError).toBeVisible();
  });
});