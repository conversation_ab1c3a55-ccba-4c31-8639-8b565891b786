name: Publish
on:
  release:
    types: [published]
jobs:
  publish-npm:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-node@v4
        with:
          node-version: 18
          registry-url: https://registry.npmjs.org/
      - run: npm ci
      - run: npx playwright install --with-deps
      - run: npm run build
      - run: npm run lint
      - run: npm run ctest
      - run: npm publish --provenance
        env:
          NODE_AUTH_TOKEN: ${{secrets.NPM_TOKEN}}
