// tests/form-validation.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Form Validation - Mandatory and Optional Fields', () => {
  test('should show phone number required error when left empty', async ({ page }) => {
    // 1. Navigate and verify load
    await page.goto('https://emlabsform.onrender.com/', { waitUntil: 'load', timeout: 10000 })
      .catch(() => test.fail(true, 'Page failed to load'));

    // 2. First Name (optional) - leave blank
    const firstName = page.getByLabel('First Name');
    await expect(firstName).toBeVisible({ timeout: 5000 })
      .catch(() => test.fail(true, 'First Name field missing'));
    await firstName.fill('');

    // 3. Last Name (optional) - leave blank
    const lastName = page.getByLabel('Last Name');
    await expect(lastName).toBeVisible({ timeout: 5000 })
      .catch(() => test.fail(true, 'Last Name field missing'));
    await lastName.fill('');

    // 4. Date of Birth (mandatory)
    const dob = page.getByLabel('Date of Birth');
    await expect(dob).toBeVisible({ timeout: 5000 })
      .catch(() => test.fail(true, 'Date of Birth field missing'));
    await dob.fill('25-Jun-2006');

    // 5. Phone Number (mandatory) - leave blank
    const phone = page.getByLabel('Phone Number');
    await expect(phone).toBeVisible({ timeout: 5000 })
      .catch(() => test.fail(true, 'Phone Number field missing'));
    await phone.fill('');

    // 6. Email (mandatory)
    const email = page.getByLabel('Email');
    await expect(email).toBeVisible({ timeout: 5000 })
      .catch(() => test.fail(true, 'Email field missing'));
    await email.fill('<EMAIL>');

    // 7. Website (optional) - leave blank
    const website = page.getByLabel('Website');
    await expect(website).toBeVisible({ timeout: 5000 })
      .catch(() => test.fail(true, 'Website field missing'));
    await website.fill('');

    // 8. Submit button
    const submitBtn = page.getByRole('button', { name: 'Submit' });
    await expect(submitBtn).toBeVisible({ timeout: 5000 })
      .catch(() => test.fail(true, 'Submit button missing'));
    await submitBtn.click();

    // 9. Verify error message for Phone Number
    const phoneError = page.getByText('Enter a number for this field.');
    await expect(phoneError).toBeVisible({ timeout: 10000 })
      .catch(() => test.fail(true, 'Phone Number error message not displayed'));
  });
});