import { test, expect } from '@playwright/test';

test.describe('Form Submission with Other Gender Selection', () => {
  test('should fill form, select Others gender, and submit without errors', async ({ page }) => {
    // 1. Navigate and verify page load
    await page.goto('https://form2-4y5z.onrender.com/', { timeout: 10000 });
    await expect(
      page,
      'Form page failed to load'
    ).toHaveURL('https://form2-4y5z.onrender.com/', { timeout: 10000 });

    // 2. First Name: optional, visible, fill
    const firstName = page.getByLabel('First Name');
    await expect(firstName, 'First Name field missing').toBeVisible({ timeout: 5000 });
    await expect(firstName, 'First Name should be optional').not.toHaveAttribute('required');
    await firstName.fill('JohnDOE');

    // 3. Last Name: optional, visible, fill
    const lastName = page.getByLabel('Last Name');
    await expect(lastName, 'Last Name field missing').toBeVisible({ timeout: 5000 });
    await expect(lastName, 'Last Name should be optional').not.toHaveAttribute('required');
    await lastName.fill('Smith');

    // 4. Phone Number: mandatory, visible, fill
    const phone = page.getByLabel('Phone Number');
    await expect(phone, 'Phone Number field missing').toBeVisible({ timeout: 5000 });
    await expect(phone, 'Phone Number should be mandatory').toHaveAttribute('required');
    await phone.fill('123456789');

    // 5. Email: mandatory, visible, fill
    const email = page.getByLabel('Email');
    await expect(email, 'Email field missing').toBeVisible({ timeout: 5000 });
    await expect(email, 'Email should be mandatory').toHaveAttribute('required');
    await email.fill('<EMAIL>');

    // 6. Date of Birth: present, visible, fill
    const dob = page.getByLabel('Date of Birth');
    await expect(dob, 'Date of Birth field missing').toBeVisible({ timeout: 5000 });
    await dob.fill('03-Jun-2007');

    // 7. Website: optional, visible, fill
    const website = page.getByLabel('Website');
    await expect(website, 'Website field missing').toBeVisible({ timeout: 5000 });
    await expect(website, 'Website should be optional').not.toHaveAttribute('required');
    await website.fill('www.domain.com');

    // 8. Gender: mandatory, visible, select Others
    const gender = page.getByLabel('Gender');
    await expect(gender, "Gender field missing").toBeVisible({ timeout: 5000 });
    await expect(gender, 'Gender should be mandatory').toHaveAttribute('required');
    await gender.selectOption('Others');

    // 9. Other Gender: appears, mandatory, visible, fill
    const otherGender = page.getByLabel('Other Gender');
    await expect(otherGender, 'Other Gender field missing').toBeVisible({ timeout: 5000 });
    await expect(otherGender, 'Other Gender should be mandatory').toHaveAttribute('required');
    await otherGender.fill('Nonbinary');

    // 10. Submit button: visible, enabled, click
    const submitBtn = page.getByRole('button', { name: 'Submit' });
    await expect(submitBtn, 'Submit button missing').toBeVisible({ timeout: 10000 });
    await expect(submitBtn, 'Submit button should be enabled').toBeEnabled();
    await submitBtn.click();

    // 11. Verify absence of "error" text
    const errorLocator = page.locator(':scope:has-text("error")');
    await expect(
      errorLocator,
      'Error message detected after form submission'
    ).not.toBeVisible({ timeout: 10000 });
  });
});