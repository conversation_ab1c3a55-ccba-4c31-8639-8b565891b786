import psycopg2
import os
from dotenv import load_dotenv
load_dotenv() 
def execute_schema(sql_file_path):
    conn = psycopg2.connect(
        dbname="QATOOL",
        user="postgres",
        password= os.getenv("DB_PASSWORD"),
        host="localhost",
        port="5432"
    )

    cur = conn.cursor()
    with open(sql_file_path, 'r') as f:
        cur.execute(f.read())

    conn.commit()
    cur.close()
    conn.close()
    print("✅ Schema applied successfully")

if __name__ == "__main__":
    execute_schema("schema.sql")
