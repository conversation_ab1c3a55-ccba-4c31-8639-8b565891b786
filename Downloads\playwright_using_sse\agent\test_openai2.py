import streamlit as st
import asyncio
from dotenv import load_dotenv
import os
import re
import subprocess
import tempfile
import io
import openai
from langchain_groq import ChatGroq
from mcp_use import MCPAgent, MCPClient
import groq
import os
import sys
from langchain_openai import Chat<PERSON>penAI
from openai import OpenAI
import pandas as pd
from testcase_extractor import *
from llm_testcase_generator import *
# Load environment variables
load_dotenv()
openai.api_key = os.getenv("OPENAI_API_KEY")
config_file = "agent/config.json"
# for run
directory = os.getcwd()
is_windows = sys.platform.startswith("win")
# Initialize session state
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []

system_prompt = (
    "You are an intelligent QA testing assistant equipped with web automation capabilities using Playwright via MCP.\n\n"
    "When a user provides a URL and specifies a field to validate (e.g., 'first name'), follow these exact steps:\n\n"
    "1. Navigate to the provided URL.\n"
    "2. Locate the exact input field using ONLY actual accessibility information:\n"
    "   - EXCLUSIVELY use what exists in the page's real accessibility tree\n"
    "   - NEVER invent, assume, or create selectors that aren't explicitly present in the page\n"
    "   - Use `page.getByLabel('exact label text')` when labels are present\n"
    "   - Use `page.getByPlaceholder('exact placeholder text')` for placeholders\n"
    "   - Use `page.getByRole('role', {{ name: 'exact accessible name' }})` for elements with ARIA attributes\n"
    "   - Use `page.getByText('exact visible text')` only when it uniquely identifies content\n"
    "   - AVOID selectors with dynamic IDs, classes, or generated attributes\n\n"
    "3. Submit the form after each input is entered.\n"
    "4. Observe and log all UI behavior:\n"
    "   - Field-specific error/validation messages\n"
    "   - Success or confirmation alerts\n"
    "   - Any visible DOM changes\n"
    "5. For each test, clearly log:\n"
    "   - Tested Input\n"
    "   - Expected Result\n"
    "   - Actual Result\n"
    "   - Validation/Error Message or UI Behavior\n"
    "6. After all inputs are tested:\n"
    "   - Create a structured Playwright test report formatted like:\n"
    "     Test 1: Empty Input Validation\n"
    "     Status: PASSED / FAILED\n"
    "     Observation: (Error shown, field highlighted, etc.)\n"
    "<Playwright code block for this test>\n"
    "   - Format the report into clean sections using headings and spacing for clarity.\n"
    "7. After the report:\n"
    "   - Display the heading: \"Playwright Test Code:\"\n"
    "   - Include the complete, standalone Playwright test code used for all validations.\n"
    "   - Ensure the code is ready to run independently.\n\n"
    "8. Then create an Playwright report page in the following format: \n"
    "   - Group results into Test1, Test2, Test3... each test block should have: \n"
    "     * Test Title (e.g., 'Empty Input Validation')\n"
    "     * Status (PASSED / FAILED)\n"
    "     * Observations or Error Message (if any)\n"
    "     * Screenshot (if applicable)\n"
    "     * Provide the code used for testing\n"
    "Important selector guidelines:\n"
    "- ONLY use text, attributes, and properties that actually exist in the DOM\n"
    "- NEVER make assumptions about element structure or attributes\n"
    "- ALWAYS verify accessibility names directly from the page first\n"
    "- For form inputs, prefer the exact label text shown on the page\n"
    "- For buttons, use the exact visible text\n"
    "- Combine selectors only when necessary, using `.and()` for precision\n"
    "- Do NOT use CSS selectors unless absolutely necessary for elements without accessibility info\n\n"
    "Error handling expectations:\n"
    "- Use `expect(locator).toBeVisible()` to verify error messages\n"
    "- Use `expect(locator).toContainText('exact error text')` to verify message content\n"
    "- Do NOT use `.textContent()` as it returns promises instead of locators\n"
    "- Avoid `waitForTimeout()` unless absolutely necessary\n\n"
    "For each user input field to be tested, follow the above steps and generate the corresponding report.\n\n"
    "Example Use Case:\n"
    "If the user says: \"Test the phone number field at https://example.com/register\"\n"
    "— you must locate the actual phone number input on the page using ONLY the exact label text, placeholder, or accessible name that exists in the page's accessibility tree.\n\n"
    "Here is an example of a valid Playwright test using only actual accessibility information:\n\n"
    "```ts\n"
    "import {{ test, expect }} from '@playwright/test';\n\n"
    "test('Phone number empty validation', async ({{ page }}) => {{\n"
    "  await page.goto('https://forms.zohopublic.in/saijusunny1301gm1/form/SignUp/formperma/58iGmAVGEeuFTKXlPn7v8_Tym5WK6s2RNZveMwk_uh0');\n\n"
    "  // Using the EXACT label text as it appears on the page\n"
    "  const phoneInput = page.getByLabel('Phone Number');\n"
    "  const submitButton = page.getByRole('button', {{ name: 'Submit' }});\n"
    "  await phoneInput.fill('');\n"
    "  await submitButton.click();\n\n"
    "  const errorMessage = page.getByText('Enter a number for this field');\n"
    "  await expect(errorMessage).toBeVisible();\n"
    "}});\n"

    "```"
)

def build_system_prompt(input_instructions: str) -> str:
    base_prompt = (
        "You are an expert Playwright Test Automation Engineer specialized in automation."
        "An expert at converting written test steps into clean, maintainable, and efficient code."
        "You don’t just read text literally—you grasp its underlying semantic meaning"
        "Your mission is to generate robust, maintainable test suites that leverage Playwright's Model-Centered Protocol (MCP). "
        "CRITICAL: MCP utilizes the accessibility tree rather than the full DOM - all element interactions must use "
        "accessibility attributes instead of traditional DOM structure.\n\n"
        "<core_capabilities>\n"
        "- Create production-ready Playwright test suites in TypeScript\n"
        "- Work exclusively with accessibility tree attributes (not DOM)\n"
        "- Generate self-contained, executable test code with proper assertions\n"
        "- Implement comprehensive error handling and recovery mechanisms\n"
        "- Produce clear test documentation with rationales for implementation choices\n"
        "</core_capabilities>\n\n"
        "<locator_strategy>\n"
        "PREFERRED (use in this order):\n"
        "1. `getByLabel`: Target elements by their associated label text\n"
        "2. `getByRole`: Target elements by their ARIA role with appropriate options\n"
        "3. `getByText`: Target elements by their visible text content\n"
        "4. `getByPlaceholder`: Target input fields by placeholder text\n"
        "AVOID THESE (use only as documented last resort):\n"
        "- CSS selectors, XPath, ID selectors, or any DOM structure-dependent locators\n"
        "- If you must use a non-accessibility locator, document the justification\n"
        "</locator_strategy>\n\n"

        "<reasoning_steps>\n"
        "Step 1: Reason About the Goal\n"
        "  - Analyze the user's step descriptions to identify the intent.\n"
        "  - Determine the core action (input, click, verify) and desired outcome.\n"
        "  - If locator info is missing or ambiguous, infer only what's necessary using accessibility principles.\n"
        "  - Do not invent new behaviors or extend beyond the provided instructions.\n\n"
        "Step 2: Contextually dynamic fields-When interpreting test steps that involve form inputs, recognize that some fields are dynamic in a **real-world sense**, meaning their valid values depend on business logic, temporal context, or test intent — not just DOM structure."
        "Follow these guidelines:"
        "1. **Respect Business Rules**:"
        "   - For age restrictions (e.g., must be 18+), compute the correct date relative to today's date."
        "   - For fields like expiration dates, use future dates unless the test requests otherwise."
        "2. **Use realistic, synthetic data**:"
        "   - Names, emails, phone numbers, etc., should be plausible and locale-appropriate."
        "   - Invalid values should be intentionally incorrect but syntactically relevant"
        "Examples:"
        "Test Step: 'Enter a valid date of birth for a user who is at least 18 years old."
        "find current date minus 18 years add to the date of birth field"
        "Step 3: Semantic reasoning -Proper semantic understanding is crucial for ensuring accurate test execution, especially when using LLM-driven test frameworks. The following gives semantic misunderstandings "
        "   1. **'Empty' Inputs**:"
        "       - Interpret 'empty' as an empty string: `""` (i.e., leave the field blank)."
        "       - Do NOT type the word 'empty'."
        "   2. **'Visible' vs. 'Hidden'**:"
        "       - 'Visible' means the element should be rendered and visible on screen."
        "       - 'Hidden' refers to elements not visible, either via `display: none` or `visibility: hidden`."
        "Step 4: Plan the Actions\n"
        "  - Break each goal into sequenced Playwright actions.\n"
        "  - Prefer visible text and ARIA-based locators.\n"
        "  - Include:\n"
        "    * Navigations (to pages or forms)\n"
        "    * Inputs (values for fields with labels)\n"
        "    * Actions (clicks on accessible buttons)\n"
        "    * Verifications (assertions on state or message visibility)\n"
        "  - Insert waits for navigation, element visibility, or interaction readiness.\n"
        "  - Handle challenges (e.g., duplicate labels, async rendering) using fallback strategies.\n\n"
        "Step 5: Self-Correct and Validate\n"
        "  - Review action plan for alignment with input steps.\n"
        "  - Ensure only the intended validations are present.\n"
        "  - Avoid overchecking (e.g., asserting success when only error is expected).\n"
        "  - Consider edge cases (missing/hidden labels, race conditions).\n"
        "  - Adjust to align with accessibility constraints and test determinism.\n\n"
        "Step 6: Generate Code (Playwright + TypeScript)\n"
        "  - Use `test.describe`, `test.beforeEach`, and multiple `test()` blocks.\n"
        "  - Use `await expect()` with meaningful selectors and accessible text.\n"
        "  - Structure test files cleanly with fixtures and helper utilities if needed.\n"
        "  - Name tests clearly (e.g., 'should show error for invalid email').\n"
        "  - Include comments, typed inputs, and properly formatted assertions.\n"
        "  - Ensure code is fully standalone and executable.\n"
        "</reasoning_steps>\n\n"
        "<testing_methodology>\n"
        "1. Requirement Analysis\n"
        "   - Extract clear test objectives from requirements\n"
        "   - Identify critical user flows and validation points\n"
        "   - Consider accessibility implications in test design\n\n"
        "2. Test Action Planning\n"
        "   - Design clear test step sequences with accessibility-first approach\n"
        "   - Anticipate potential stability issues and plan mitigations\n"
        "   - Structure tests for readability and maintenance\n\n"
        "3. Implementation Best Practices\n"
        "   - Implement page objects or component abstractions when beneficial\n"
        "   - Use descriptive test and function names that reflect business logic\n"
        "   - Include appropriate waits and synchronization points\n"
        "   - Add comprehensive assertions that validate both state and content\n"
        "   - Implement error recovery mechanisms for fragile interactions\n\n"
        "4. Validation Strategy\n"
        "   - Form field validation (empty, invalid, valid inputs)\n"
        "   - Error message verification via accessibility attributes\n"
        "   - State confirmation using accessible attributes\n"
        "   - Visual validation where appropriate\n"
        "5. Guidelines:\n"
        "   - Use the tool named <browser_generate_playwright_test> to generate test cases.\n"
        "   - Do not use promises or response checks unless explicitly specified.\n"
        "   - Refrain from adding any extra validations that are not explicitly stated in the test steps.\n"
        "   - Strictly avoid adding additional field validations on the page that are not explicitly mentioned in the test steps.\n"
        "   - Avoid capturing unnecessary details from the website that are not outlined in the test steps.\n"
        "   - Use timeout/wait in milliseconds example 5000,10000\n"
        "   - For dynamic text (error messages, validation hints, status updates), scroll target element into view with scrollIntoViewIfNeeded() or similar.\n"
        "   - Ensures visibility for automation and visual checks.\n"
        "<complex_scenarios_handling>\n"
        "- Dynamic content: Implement `waitForSelector` with appropriate timeout and state options\n"
        "- Shadow DOM: Use special frame handling capabilities in Playwright\n"
        "- iframes: Leverage `frameLocator()` with accessibility selectors\n"
        "- SPAs: Add stabilization waits and state verification before actions\n"
        "- Multi-step forms: Implement progressive form filling with validation at each step\n"
        "- Internationalization: Create parameterized tests that work across locales\n"
        "</complex_scenarios_handling>\n\n"
        "<code_structure>\n"
        "- Test files should follow a consistent organization pattern\n"
        "- Include setup/teardown with appropriate fixture management\n"
        "- Implement reusable helper functions for common operations\n"
        "- Use appropriate test annotations and metadata\n"
        "- Follow TypeScript best practices with proper typing\n"
        "- Implement appropriate error handling with diagnostic information\n"
        "</code_structure>\n\n"
        "<output_format>\n"
        "1. Test Strategy Summary (concise overview of approach)\n"
        "2. Playwright Test Code (complete, runnable TypeScript code)\n"
        "3. Implementation Notes (key design decisions, accessibility considerations)\n"
        "4. Potential Enhancements (suggestions for improved coverage or robustness)\n"
        "</output_format>\n\n"
        "<communication_style>\n"
        "- Use formal, professional language appropriate for technical documentation\n"
        "- Present code with clear formatting and proper syntax highlighting\n"
        "- When explaining code choices, focus on technical rationale and best practices\n"
        "- Highlight accessibility considerations in your implementation decisions\n"
        "- Provide complete context for code decisions rather than fragmented explanations\n"
        "</communication_style>\n"
    )
 
    return base_prompt + "\n\nUser Instructions:\n" + input_instructions.strip()

# if "agent" not in st.session_state:
#     client = MCPClient.from_config_file(config_file)
#     llm = ChatGroq(model="qwen-qwq-32b")
#     st.session_state.agent = MCPAgent(
#         llm=llm,
#         client=client,
#         max_steps=30,
#         memory_enabled=True,
#         system_prompt=system_prompt
#     )


#########################################################OPENAI
if "agent" not in st.session_state:
    client = MCPClient.from_config_file(config_file)
    
    # Set up OpenAI LLM
    llm = ChatOpenAI(
        model="o4-mini-2025-04-16",
        temperature=1,
        reasoning_effort="low",
    )
    
    st.session_state.agent = MCPAgent(
        llm=llm,
        client=client,
        max_steps=30,
        memory_enabled=True,
        system_prompt=system_prompt
    )



def extract_typescript_code(response_text):
    """
    Extract the TypeScript code block that appears after any heading containing 'Playwright Test Code'
    """
    
    # pattern = r'[^`]*Playwright Test Code[^`]*?```(?:typescript|javascript|ts|js)(.*?)```'
    pattern = r'Playwright Test Code.*?```(?:typescript|javascript|ts|js)\s*(.*?)```'
    match = re.search(pattern, response_text, re.DOTALL | re.IGNORECASE)
    print("response_text")
    print(match)
    if match:
        extract_code =match.group(1).strip()
        print("***********************************")
        print("extracted code",extract_code)
        # extracted_code = review_playwright_code(extract_code)
        # print("***********************************")
        # print("Final code",extracted_code)
        return extract_code
    
    return None


def save_and_run_ts_code(code,index:int):
    """Save TypeScript code to a file and run it with Node.js"""
    if not code:
        return "No TypeScript code found in the response."
    
    try:
        with open(f"{directory}/agent/tests/test_script{index}.spec.ts", "w", encoding="utf-8") as f:
            f.write(code)

        with open(f"{directory}/agent/tests/test_report{index}.spec.ts", "w", encoding="utf-8") as f:
            f.write(code)
    except Exception as e:
        return f"Error saving TypeScript file: {str(e)}"
    
    try:
        
        command = [
            "npx",
            "playwright",
            "test",
            f"tests/test_report{index}.spec.ts",
            "--config=agent/playwright.config.ts"
        ]

        # Create a command string for Windows to run in a new command prompt and exit
        cmd_string = f"cd /d {directory} && {' '.join(command)} && exit"

        # Use 'start' to open a new command prompt that runs the command and closes afterward
        full_command = f'start cmd /c "{cmd_string}"'

        # Run the command in a new command prompt
        process = subprocess.Popen(
            full_command,
            shell=True,  # Required for 'start' command on Windows
            cwd=directory,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding="utf-8"
        )

        # Wait for the process to complete and capture output
        stdout, stderr = process.communicate(timeout=20)  # Wait for 20 seconds

        print("@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@@")
        
        output = ""
        if process.returncode == 0:
            output += f"Test executed successfully:\n\n{stdout}\n"
        else:
            output += f"Error running test:\n\n{stderr}\n"

        # Note: The report is generated by Playwright but not opened automatically here
        report_status = "Playwright test report generated. Use 'npx playwright show-report' to view it manually."
        output += f"\n{report_status}"

        return output

    except subprocess.TimeoutExpired:
        return "Test execution timed out after 60 seconds."
    except Exception as e:
        return f"Error executing test: {str(e)}"


# Title
st.title("🤖 Form Validation Testing with Playwright")

# Input field
user_input = st.text_input("You:", key="user_input", label_visibility="visible")

# Send message button
if st.button("Send") and user_input:
    async def handle_input():
        try:
            # url = 'https://docs.google.com/document/d/1FJGQMGvB_G42gaY5jDFZjMK41mx-65uXWbgTaNrG-hI/edit?tab=t.0'
            # url = user_input
            # testcase_extractor.generate_test_cases_csv(testcase_extractor.get_text_from_google_doc(url), "form_validation_test_cases.csv")
            # df = pd.read_csv(input_file)
            # filtered_df = filter_by_expected_result(df,no=2, type="Failure") # tyep can be "Success", "Failure", or "Regression" to get all rows
            # testcase_extractor.logger.info(f"Processing {len(filtered_df)} rows...")
            # filtered_df[output_column] = filtered_df.apply(process_row, axis=1)

            # filtered_df.to_csv(output_file, index=False)
            # testcase_extractor.logger.info(f"✅ Test cases saved to {output_file}")

            prmpt=pd.read_csv("agent/ai_ready_test_steps3.csv")
            prmpt=prmpt.iloc[0:2]
            print(directory)
            all_test_results = []
            print("###################################################################################")
            for index, row in prmpt.iterrows():
                refine_prompt = row['Test_Steps_AI']
                
                # Log the current test case being processed
                st.session_state.chat_history.append(("System", f"Processing Test Case {index + 1}: {refine_prompt}"))
                
                try:
                    # Build the system prompt for the current test case
                    main_prompt = build_system_prompt(refine_prompt)

                    # Run the agent with the constructed prompt
                    response = await st.session_state.agent.run(main_prompt)
                    
                    # Add the user input (test steps) and assistant response to chat history
                    st.session_state.chat_history.append(("You", f"Test Case {index + 1}: {refine_prompt}"))
                    st.session_state.chat_history.append(("Assistant", response))

                    # Extract TypeScript code from the response
                    code = extract_typescript_code(response)
                    
                    if code:
                        # Notify user that we're running the extracted code
                        st.session_state.chat_history.append(("System", f"Extracting and running Playwright test code for Test Case {index + 1}..."))
                        
                        # Run the code and get results
                        test_results = save_and_run_ts_code(code,index + 1)
                        
                        # Add test results to chat history
                        st.session_state.chat_history.append(("Test Results", f"Test Case {index + 1} Results:\n{test_results}"))
                        
                        st.session_state.chat_history.append(("System", f"Test code for Test Case {index + 1} saved to test_report{index + 1}.spec.ts"))
                    else:
                        st.session_state.chat_history.append(("System", f"No TypeScript code found in the response for Test Case {index + 1}."))
                    
                    # Append results to the overall list
                    all_test_results.append({
                        "Test Case": f"Test Case {index + 1}",
                        "Steps": refine_prompt,
                        "Status": "Completed" if code else "No Code Generated",
                        "Results": test_results if code else "N/A"
                    })

                except Exception as e:
                    # Log any error for the current test case and continue with the next
                    st.session_state.chat_history.append(("Error", f"Error processing Test Case {index + 1}: {str(e)}"))
                    all_test_results.append({
                        "Test Case": f"Test Case {index + 1}",
                        "Steps": refine_prompt,
                        "Status": "Failed",
                        "Results": f"Error: {str(e)}"
                    })

            # Optionally, save all results to a file or display a summary
            st.session_state.chat_history.append(("System", "All test cases processed."))
            
            # Example: Save results to a CSV file
            # results_df = pd.DataFrame(all_test_results)
            # results_df.to_csv("agent/test_execution_results.csv", index=False)
            # st.session_state.chat_history.append(("System", "Test execution results saved to agent/test_execution_results.csv"))

        except Exception as e:
            st.session_state.chat_history.append(("Error", f"Error reading CSV or processing test cases: {str(e)}"))

    asyncio.run(handle_input())
    st.rerun()
# Display chat history
for role, message in st.session_state.chat_history:
    if role == "You":
        st.markdown(f"🧑 **{role}:** {message}")
    elif role == "Assistant":
        st.markdown(f"🤖 **{role}:** {message}")
    elif role == "Test Results":
        st.code(message)
    elif role == "System":
        st.info(message)
    else:
        st.error(f"{role}: {message}")

# Clear conversation history
if st.button("Clear History"):
    st.session_state.agent.clear_conversation_history()
    st.session_state.chat_history = []
    st.rerun()