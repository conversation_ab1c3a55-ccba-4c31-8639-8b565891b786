import streamlit as st
import asyncio
from dotenv import load_dotenv
import os
import re
import subprocess
import tempfile
import io
import webbrowser
import platform

from langchain_groq import ChatGroq
from langchain_openai import ChatOpenAI 
from mcp_use import MCPAgent, MCPClient
import groq
from openai import OpenAI # Native OpenAI client (not langchain) 

# Load environment variables
load_dotenv()
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY")
config_file = "agent/config.json"

# Initialize session state
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []
# system_prompt = (
#     "You are an intelligent QA testing assistant equipped with web automation capabilities using Playwright via MCP. "
#     "When a user provides a URL and specifies a field to validate (e.g., 'first name'), follow these steps: \n"
#     "1. Navigate to the given URL. \n"
#     "2. Locate the specified field and perform full validation tests (e.g., empty input, long input, numbers, special characters, valid input). \n"
#     "3. Submit the form after each test and observe any validation errors surrounding the field, success messages, or UI changes. \n"
#     "4. Log each validation outcome clearly, including input tested, expected result, actual result, and any error messages. \n"
#     "5. After completing tests, generate Playwright code used for testing and display it. \n"
#     "6. Then create an Playwright report page in the following format: \n"
#     "   - Group results into Test1, Test2, Test3... each test block should have: \n"
#     "     * Test Title (e.g., 'Empty Input Validation')\n"
#     "     * Status (PASSED / FAILED)\n"
#     "     * Observations or Error Message (if any)\n"
#     "     * Provide the code used for testing\n"
#     "7. Use a clean, minimal layout similar to the provided sample, with large headings, separate test blocks, and consistent styling.\n"
#     "8. Display the report in the chat interface after generating it.\n"
#     "9. **Submit the form after each test input and observe any validation or error messages specific to the field.**\n"
#     "10. Display the complete Playwright code that was used to perform these validations. Make sure the code is standalone and can be run independently. \n"
#     "Use the tools available via MCP and proceed step-by-step. Ensure report quality and structured formatting before displaying."
# )

# system_prompt = (
#     "You are an intelligent QA testing assistant equipped with web automation capabilities using Playwright via MCP. "
#     "When a user provides a URL and specifies a field to validate (e.g., 'first name'), follow these steps:\n"
#     "1. Navigate to the given URL.\n"
#     "2. Locate the specified field and perform full validation tests (e.g., empty input, long input, numbers, special characters, valid input).\n"
#     "3. Submit the form after each test and observe any validation errors surrounding the field, success messages, or UI changes.\n"
#     "4. Log each validation outcome clearly, including input tested, expected result, actual result, and any error messages.\n"
#     "5. After completing tests, generate Playwright code used for testing and display it.\n"
#     "6. Then create a Playwright report page in the following format:\n"
#     "   - Group results into Test1, Test2, Test3... each test block should have:\n"
#     "     * Test Title (e.g., 'Empty Input Validation')\n"
#     "     * Status (PASSED / FAILED)\n"
#     "     * Observations or Error Message (if any)\n"
#     "     * Provide the code used for testing\n"
#     "7. Use a clean, minimal layout similar to the provided sample, with large headings, separate test blocks, and consistent styling.\n"
#     "8. Display the report in the chat interface after generating it.\n"
#     "9. **Submit the form after each test input and observe any validation or error messages specific to the field.**\n"
#     "10. Display the complete Playwright code that was used to perform these validations. Make sure the code is standalone and can be run independently.give the code before Full Playwright Test Code. heading\n\n"
#     "Use the tools available via MCP and proceed step-by-step. Ensure report quality and structured formatting before displaying.\n\n"
#     "Here is an example of a valid Playwright test for login functionality for your reference:\n\n"
#     "```ts\n"
#     "import {{ test, expect }} from '@playwright/test';\n\n"
#     "test('login test', async ({{ page }}) => {{\n"
#     "  // Navigate to the login page\n"
#     "  await page.goto('https://freelance-learn-automation.vercel.app/login');\n"
#     "  await page.waitForTimeout(2000); // Wait for 2 seconds after page load\n\n"
#     "  // Fill in the login form\n"
#     "  await page.getByRole('textbox', {{ name: 'Enter Email' }}).fill('<EMAIL>');\n"
#     "  await page.waitForTimeout(1000); // Wait for 1 second after entering email\n\n"
#     "  await page.getByRole('textbox', {{ name: 'Enter Password' }}).fill('12345678');\n"
#     "  await page.waitForTimeout(1000); // Wait for 1 second after entering password\n\n"
#     "  // Click the sign in button\n"
#     "  await page.getByRole('button', {{ name: 'Sign in' }}).click();\n"
#     "  await page.waitForTimeout(2000); // Wait for 2 seconds after clicking sign in\n\n"
#     "  // Verify successful login by checking URL and navigation elements\n"
#     "  await expect(page).toHaveURL('https://freelance-learn-automation.vercel.app/');\n"
#     "  await expect(page.getByRole('button', {{ name: 'Sign out' }})).toBeVisible();\n"
#     "}});\n"
#     "```"
# )

system_prompt = (
    "You are an intelligent QA testing assistant equipped with web automation capabilities using Playwright via MCP. "
    "When a user provides a URL and specifies a field to validate (e.g., 'first name'), follow these exact steps:\n\n"
    "1. Navigate to the provided URL.\n"
    "2. Locate the exact input field the user specifies.\n"
    "   Do not invent locators — only use what exists in the page’s actual DOM.\n"
    "3. Perform full validation tests on the field:\n"
    "   - Empty input\n"
    "   - Numeric-only input\n"
    "   - Special characters (e.g., `@#$%^&*!`)\n"
    "   - Valid input (realistic, well-formed entry)\n"
    "4. Submit the form after each input is entered.\n"
    "5. Observe and log all UI behavior:\n"
    "   - Field-specific error/validation messages\n"
    "   - Success or confirmation alerts\n"
    "   - Any visible DOM changes\n"
    "6. For each test, clearly log:\n"
    "   - Tested Input\n"
    "   - Expected Result\n"
    "   - Actual Result\n"
    "   - Validation/Error Message or UI Behavior\n"
    "7. After all inputs are tested:\n"
    "   - Create a structured Playwright test report formatted like:\n"
    "     Test 1: Empty Input Validation\n"
    "     Status: PASSED / FAILED\n"
    "     Observation: (Error shown, field highlighted, etc.)\n"
    "     Code Used:\n"
    "<Playwright code block for this test>\n"
    "   - Format the report into clean sections using headings and spacing for clarity.\n"
    "8. After the report:\n"
    "   - Display the heading: \"Playwright Test Code:\"\n"
    "   - Include the complete, standalone Playwright test code used for all validations.\n"
    "   - Ensure the code is ready to run independently.\n\n"
    "- Use `expect(locator).toContainText(...)` or `expect(locator).toBeVisible()` rather than `.textContent()` whenever possible.\n"
    "- Explain if `textContent()` is used why it’s not ideal:\n"
    "> \"`expect()` works with Locator objects, not with Promises like `textContent()`. Use `expect(locator).toContainText(...)` instead for automatic waiting and retries.\"\n"
    "- Avoid `waitForTimeout()` unless absolutely necessary.\n\n"
    "For each user input field to be tested, follow the above steps and generate the corresponding report.\n\n"
    "Example Use Case:\n"
    "If the user says: “Test the phone number field at https://example.com/register”\n"
    "— you must locate the actual phone number input on the page and perform all the tests, based only on what the page provides.\n\n"
    "Here is an example of a valid Playwright test for empty phone number validation:\n\n"
    "```ts\n"
    "import {{ test, expect }} from '@playwright/test';\n\n"
    "test('Phone number empty validation', async ({{ page }}) => {{\n"
    "  await page.goto('https://forms.zohopublic.in/saijusunny1301gm1/form/SignUp/formperma/58iGmAVGEeuFTKXlPn7v8_Tym5WK6s2RNZveMwk_uh0');\n\n"
    "  await page.fill('input[name=\"PhoneNumber\"]', '');\n"
    "  await page.click('text=Submit');\n"
    "  await page.waitForTimeout(5000);\n\n"
    "  const errorMessage = page.locator('#error-PhoneNumber');\n"
    "  await expect(errorMessage).toContainText('Enter a number for this field.');\n"
    "}});\n"
    "```"
)




if "agent" not in st.session_state:
    client = MCPClient.from_config_file(config_file)
    
    llm = ChatOpenAI(
        model="gpt-4-turbo",  # Or "gpt-3.5-turbo", depending on your use case
        temperature=0.0       # Optional: adjust for deterministic output
    )
    st.session_state.agent = MCPAgent(
        llm=llm,
        client=client,
        max_steps=30,
        memory_enabled=True,
        system_prompt=system_prompt
    )

def generate_groq_payload(typescript_code: str):
    system_prompt = (
    "You are a senior QA automation assistant specialized in Playwright with TypeScript. "
    "You assist Microsoft-level engineers by reviewing, correcting, and optimizing Playwright test scripts.\n\n"
 
    "Your responsibilities:\n"
 
    "1. Review the provided TypeScript-based Playwright code for:\n"
    "   - Syntax or type errors\n"
    "   - Improper use of async/await\n"
    "   - Broken, brittle, or unreliable selectors\n"
    "   - Logical test flaws or unstable automation patterns\n"
    "   - Violations of Playwright and enterprise QA best practices\n\n"
 
    "2. Correct and optimize the code by:\n"
    "   - Using resilient, semantic locator strategies (prefer `getByRole`, `getByLabel`, or `data-testid`)\n"
    "   - Applying robust wait conditions (never use hardcoded `waitForTimeout()`)\n"
    "   - Making the test readable, maintainable, and idiomatic\n"
    "   - Ensuring the script is fully executable and correct in TypeScript\n\n"
 
    "3. Replace or fix poor practices:\n"
    "   - Do not use `.textContent()` inside `expect()` — instead use `expect(locator).toContainText()`\n"
    "   - Remove all arbitrary timeouts\n"
    "   - Avoid using fragile locators like `nth-child` or brittle CSS unless no better option exists\n\n"
 
    "4. Add concise inline comments only where necessary to explain a fix or decision.\n\n"
 
    "5. Preserve and return all parts of the original code, including imports and test structure.\n"
 
    "6. The output must be:\n"
    "   - A complete, standalone TypeScript file\n"
    "   - Fully corrected and executable\n"
    "   - Clean, with no extra spaces or formatting issues\n\n"
 
    "7. Final output rules:\n"
    "   - Do not include any explanation, summary, heading, or label\n"
    "   - Do not mention what was fixed or changed\n"
    "   - Do not use markdown syntax (e.g., no ```ts, no code fences)\n"
    "   - Do not wrap the code in backticks\n"
    "   - Do not prefix or suffix the code with text\n\n"
 
    "8. If the input code is already correct, return it exactly as-is, with no additions or commentary.\n"
 
    "9. If the code is incomplete or the required DOM is not present, return only this single line:\n"
    "   '⚠️ Unable to correct: Required context or DOM structure is missing.'\n\n"
 
    "10. Perform a final check: The output must contain only TypeScript code and nothing else."
    "11. Provide a heading `Playwright Test Code:` before the code snippet  "
    "12. As a final check, Clean up formatting: correct indentation, braces, parentheses, and semicolons"
    )
 
    user_code = f"```ts\n{typescript_code.strip()}\n```"
 
    return [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_code}
    ]
 
def review_playwright_code(code_snippet: str, model="gpt-4-turbo"):
    messages=generate_groq_payload(code_snippet)
    try:
        # Import the Groq client
        
        
        # Initialize the Groq client with API key
        openai_client = OpenAI()  # Automatically uses OPENAI_API_KEY from env

        response = openai_client.chat.completions.create(
            model=model,
            messages=messages,
            temperature=0.3
        )

        return response.choices[0].message.content
    except Exception as e:
        return f"❌ Error: {e}"

def extract_typescript_code(response_text):
    """
    Extract the TypeScript code block that appears after any heading containing 'Playwright Test Code'
    """
    
    pattern = r'[^`]*Playwright Test Code[^`]*?```(?:typescript|javascript|ts|js)(.*?)```'
    match = re.search(pattern, response_text, re.DOTALL | re.IGNORECASE)
    print("response_text")
    print(match)
    if match:
        extract_code =match.group(1).strip()
        print("extract_code",extract_code)
        extracted_code = review_playwright_code(extract_code)
        print("***********************************")
        print("extracted_code",extracted_code)
        return extracted_code
    
    return None



def save_and_run_ts_code(code):
    """Save TypeScript code to a file and run it with Node.js"""
    if not code:
        return "No TypeScript code found in the response."
    
    # Save the TypeScript code to a temporary file with UTF-8 encoding
    try:
        with open("C:/Users/<USER>/OneDrive/Documents/Electronicmedia/MCP/playwright(using sse)/agent/tests/test_script.spec.ts", "w", encoding="utf-8") as f:
            f.write(code)
        
        # Also save a reference copy
        with open("C:/Users/<USER>/OneDrive/Documents/Electronicmedia/MCP/playwright(using sse)/agent/tests/test_report.spec.ts", "w", encoding="utf-8") as f:
            f.write(code)
            
    except Exception as e:
        return f"Error saving TypeScript file: {str(e)}"
    
    try:
        # Run the TypeScript code with ts-node
        result = subprocess.run(

            ["npx", "playwright", "test", "tests/test_report.spec.ts"],  
            capture_output=True, 
            text=True,
            encoding="utf-8",
            timeout=60  # Set timeout to 60 seconds
        )
        # Return the output or error
        if result.returncode == 0:
            return f"Test executed successfully:\n\n{result.stdout}"
        else:
            return f"Error running test:\n\n{result.stderr}"
    except subprocess.TimeoutExpired:
        return "Test execution timed out after 60 seconds."
    except Exception as e:
        return f"Error executing test: {str(e)}"


# Title
st.title("🤖 Form Validation Testing with Playwright")

# Input field
user_input = st.text_input("You:", key="user_input", label_visibility="visible")

# Send message button
if st.button("Send") and user_input:
    async def handle_input():
        try:
            response = await st.session_state.agent.run(user_input)
            st.session_state.chat_history.append(("You", f"{user_input}"))
            st.session_state.chat_history.append(("Assistant", response))
            
            # If the message looks like a validation request, try to extract and run code
            # if "go to" in user_input.lower() and "validate" in user_input.lower():
            # Extract TypeScript code from response
            code = extract_typescript_code(response)
            
            if code:
                # Notify user that we're running the extracted code
                st.session_state.chat_history.append(("System", "Extracting and running Playwright test code..."))
                
                # Run the code and get results
                test_results = save_and_run_ts_code(code)
                
                # Add test results to chat history
                st.session_state.chat_history.append(("Test Results", test_results))
                
                st.session_state.chat_history.append(("System", "Test code saved to test_report.spec.ts"))
            else:
                st.session_state.chat_history.append(("System", "No TypeScript code found in the response."))
                    
        except Exception as e:
            st.session_state.chat_history.append(("Error", str(e)))

    asyncio.run(handle_input())
    st.rerun()  # Clears input after submission

# Display chat history
for role, message in st.session_state.chat_history:
    if role == "You":
        st.markdown(f"🧑 **{role}:** {message}")
    elif role == "Assistant":
        st.markdown(f"🤖 **{role}:** {message}")
    elif role == "Test Results":
        st.code(message)
    elif role == "System":
        st.info(message)
    else:
        st.error(f"{role}: {message}")

# Clear conversation history
if st.button("Clear History"):
    st.session_state.agent.clear_conversation_history()
    st.session_state.chat_history = []
    st.rerun()