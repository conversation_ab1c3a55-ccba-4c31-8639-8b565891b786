Test_Case_ID,Test_Case_Description,First_Name,Last_Name,Phone_Number,Email,Website,Expected_Result,Error_Message,Test_Case_Category,Test_Steps
TC001,"Happy Path: All mandatory fields correctly filled with valid data, optional fields empty","""","""",123456789,<EMAIL>,"""",<PERSON>,"""",Happy Path,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Leave empty. 3. Field 'Last Name' is optional. Leave empty. 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Leave empty. 7. Submit the form. 8. Verify form submission is successful.
TC002,"Happy Path: All fields filled with valid data including optional fields and edge cases (mixed case names, subdomain email, DOB exactly 18 years ago)",<PERSON><PERSON>,<PERSON>,987654321,<EMAIL>,www.domain.com,Success,"""",Happy Path,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Enter 'JohnDOE'. 3. Field 'Last Name' is optional. Enter 'Smith'. 4. Field 'Phone Number' is mandatory. Enter '987654321'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Enter 'www.domain.com'. 7. Field 'Date of Birth' is mandatory. Enter '19-May-2007' (exactly 18 years before 19-May-2025). 8. Submit the form. 9. Verify form submission is successful.
TC003,Negative Path: Required fields empty triggers all required field error messages,"""","""","""","""","""",Failure,Phone_Number: Enter a number for this field.|Email: Enter a valid email address. (eg: <EMAIL>)|Date_of_Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.,Negative Path,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Leave empty. 3. Field 'Last Name' is optional. Leave empty. 4. Field 'Phone Number' is mandatory. Leave empty. 5. Field 'Email' is mandatory. Leave empty. 6. Field 'Website' is optional. Leave empty. 7. Field 'Date of Birth' is mandatory. Leave empty. 8. Submit the form. 9. Verify errors: Field 'Phone Number' should error with 'Enter a number for this field.' Field 'Email' should error with 'Enter a valid email address. (eg: <EMAIL>)' Field 'Date of Birth' should error with 'Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.'
TC004,Negative Path: Format validation errors for all fields with format restrictions,John123,Smith!,12345abc,user@@domain.com,http://domain.com,Failure,First_Name: First name must contain alphabetic characters only.|Last_Name: Last name must contain alphabetic characters only.|Phone_Number: Enter only numbers.|Email: Enter a valid email address. (eg: <EMAIL>)|Website: Enter a valid website. (eg: www.domain.com),Negative Path,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Enter 'John123'. 3. Field 'Last Name' is optional. Enter 'Smith!'. 4. Field 'Phone Number' is mandatory. Enter '12345abc'. 5. Field 'Email' is mandatory. Enter 'user@@domain.com'. 6. Field 'Website' is optional. Enter 'http://domain.com'. 7. Field 'Date of Birth' is mandatory. Enter '19-May-2000' (valid date to isolate format errors). 8. Submit the form. 9. Verify errors: Field 'First Name' should error with 'First name must contain alphabetic characters only.' Field 'Last Name' should error with 'Last name must contain alphabetic characters only.' Field 'Phone Number' should error with 'Enter only numbers.' Field 'Email' should error with 'Enter a valid email address. (eg: <EMAIL>)' Field 'Website' should error with 'Enter a valid website. (eg: www.domain.com)'
TC005,Negative Path: Date of Birth range validation errors (future date and under 18),"""","""",123456789,<EMAIL>,www.domain.com,Failure,Date_of_Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.,Negative Path,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Leave empty. 3. Field 'Last Name' is optional. Leave empty. 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Enter 'www.domain.com'. 7. Field 'Date of Birth' is mandatory. Enter '20-May-2007' (one day short of 18 years from 19-May-2025) and then try '20-May-2026' (future date). 8. Submit the form after each entry. 9. Verify errors: Field 'Date of Birth' should error with 'Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.'
TC006,"Edge Case: Boundary testing for names with invalid characters (hyphens, accented letters, spaces) and leading/trailing spaces",John-, José ,"""",<EMAIL>,"""",Failure,First_Name: First name must contain alphabetic characters only.|Last_Name: Last name must contain alphabetic characters only.,Edge Case,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Enter 'John-'. 3. Field 'Last Name' is optional. Enter ' José ' (with leading/trailing spaces and accented letter). 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Leave empty. 7. Field 'Date of Birth' is mandatory. Enter '19-May-2000' (valid date). 8. Submit the form. 9. Verify errors: Field 'First Name' should error with 'First name must contain alphabetic characters only.' Field 'Last Name' should error with 'Last name must contain alphabetic characters only.'
TC007,"Edge Case: Website field invalid formats (missing www., missing domain extension)","""","""",123456789,<EMAIL>,www.domain,Failure,Website: Enter a valid website. (eg: www.domain.com),Edge Case,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Leave empty. 3. Field 'Last Name' is optional. Leave empty. 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Enter 'www.domain' (missing domain extension). 7. Field 'Date of Birth' is mandatory. Enter '19-May-2000' (valid date). 8. Submit the form. 9. Verify error: Field 'Website' should error with 'Enter a valid website. (eg: www.domain.com)'
TC008,"Edge Case: Leading/trailing spaces trimmed in email and names, no error on valid trimmed input", John , Doe ,123456789, <EMAIL> ,www.domain.com,Success,"""",Edge Case,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Enter ' John ' (with leading/trailing spaces). 3. Field 'Last Name' is optional. Enter ' Doe ' (with leading/trailing spaces). 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter ' <EMAIL> ' (with leading/trailing spaces). 6. Field 'Website' is optional. Enter 'www.domain.com'. 7. Field 'Date of Birth' is mandatory. Enter '19-May-2000' (valid date). 8. Submit the form. 9. Verify form submission is successful and no errors are shown.
