Test_Case_ID,Test_Case_Description,First_Name,Last_Name,Phone_Number,Email,Website,Gender,Other_Gender,Expected_Result,Error_Message,Test_Case_Category,Test_Steps
TC001,"Happy Path: All mandatory fields valid, optional fields valid including 'Others' gender with valid other gender input",<PERSON><PERSON><PERSON>,<PERSON>,123456789,<EMAIL>,www.example.com,Others,Nonbinary,Success,,Happy Path,1. Navigate to https://form2-4y5z.onrender.com/. 2. Field 'First Name' is optional. Enter 'JohnDOE'. 3. Field 'Last Name' is optional. Enter 'Smith'. 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Enter 'www.example.com'. 7. Field 'Gender' is mandatory. Select 'Others'. 8. Field 'Other Gender' is mandatory when 'Others' selected. Enter 'Nonbinary'. 9. Submit the form. 10. Verify form submits successfully with no error messages.
TC002,Negative Path: All required fields empty triggers all required field errors,"\\""""","\\""""","\\""""","\\""""","\\""""","\\""""","\\""""",Failure,Phone_Number: Enter a number for this field.|Email: Enter a valid email address. (eg: <EMAIL>)|Date_of_Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.|Gender: Please select your gender.,Negative Path,"1. Navigate to https://form2-4y5z.onrender.com/. 2. Field 'First Name' is optional. Leave empty. 3. Field 'Last Name' is optional. Leave empty. 4. Field 'Phone Number' is mandatory. Leave empty. 5. Field 'Email' is mandatory. Leave empty. 6. Field 'Date of Birth' is mandatory. Leave empty. 7. Field 'Website' is optional. Leave empty. 8. Field 'Gender' is mandatory. Leave empty. 9. Submit the form. 10. Verify errors: Field 'Phone Number' should error with ""Enter a number for this field."". Field 'Email' should error with ""Enter a valid email address. (eg: <EMAIL>)"". Field 'Date of Birth' should error with ""Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18."". Field 'Gender' should error with ""Please select your gender.""."
TC003,"Negative Path: Format validation errors combined for names, phone, email, website, other gender, and date of birth",John123!,Smith@,12345abc!,user name@domain,http://domain.com,Others,123!@#,Failure,First_Name: First name must contain alphabetic characters only.|Last_Name: Last name must contain alphabetic characters only.|Phone_Number: Enter only numbers.|Email: Enter a valid email address. (eg: <EMAIL>)|Website: Enter a valid website. (eg: www.domain.com)|Other_Gender: Please fill out this field with characters.|Date_of_Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.,Negative Path,"1. Navigate to https://form2-4y5z.onrender.com/. 2. Field 'First Name' is optional. Enter 'John123!'. 3. Field 'Last Name' is optional. Enter 'Smith@'. 4. Field 'Phone Number' is mandatory. Enter '12345abc!'. 5. Field 'Email' is mandatory. Enter 'user name@domain'. 6. Field 'Website' is optional. Enter 'http://domain.com'. 7. Field 'Gender' is mandatory. Select 'Others'. 8. Field 'Other Gender' is mandatory when 'Others' selected. Enter '123!@#'. 9. Field 'Date of Birth' is mandatory. Enter '31-12-2010' (invalid format and under 18). 10. Submit the form. 11. Verify errors: Field 'First Name' should error with ""First name must contain alphabetic characters only."". Field 'Last Name' should error with ""Last name must contain alphabetic characters only."". Field 'Phone Number' should error with ""Enter only numbers."". Field 'Email' should error with ""Enter a valid email address. (eg: <EMAIL>)"". Field 'Website' should error with ""Enter a valid website. (eg: www.domain.com)"". Field 'Other Gender' should error with ""Please fill out this field with characters."". Field 'Date of Birth' should error with ""Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.""."
TC004,"Edge Case: Date of Birth boundary checks for exactly 18 years old, one day short of 18, and future date","\\""""","\\""""",123456789,<EMAIL>,"\\""""",Male,"\\""""",Failure,Date_of_Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.,Edge Case,"1. Navigate to https://form2-4y5z.onrender.com/. 2. Field 'First Name' is optional. Leave empty. 3. Field 'Last Name' is optional. Leave empty. 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Leave empty. 7. Field 'Gender' is mandatory. Select 'Male'. 8. Field 'Date of Birth' is mandatory. Enter '09-Jun-2007' (exactly 18 years ago). Submit form and verify success. 9. Repeat steps 1-7, then enter '10-Jun-2007' (one day short of 18). Submit form and verify error: Field 'Date of Birth' should error with ""Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18."". 10. Repeat steps 1-7, then enter '10-Jun-2026' (future date). Submit form and verify same error."
TC005,Edge Case: Leading/trailing spaces in First Name and Last Name trigger format errors, John , Smith ,123456789,<EMAIL>,"\\""""",Female,"\\""""",Failure,First_Name: First name must contain alphabetic characters only.|Last_Name: Last name must contain alphabetic characters only.,Edge Case,"1. Navigate to https://form2-4y5z.onrender.com/. 2. Field 'First Name' is optional. Enter ' John ' with leading and trailing spaces. 3. Field 'Last Name' is optional. Enter ' Smith ' with leading and trailing spaces. 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Leave empty. 7. Field 'Gender' is mandatory. Select 'Female'. 8. Submit the form. 9. Verify errors: Field 'First Name' should error with ""First name must contain alphabetic characters only."". Field 'Last Name' should error with ""Last name must contain alphabetic characters only.""."
TC006,Edge Case: Gender 'Others' selected with valid other gender input then changed to 'Female' clears and disables other gender validation,John,Doe,123456789,<EMAIL>,"\\""""",Others,Nonbinary,Success,,Edge Case,"1. Navigate to https://form2-4y5z.onrender.com/. 2. Field 'First Name' is optional. Enter 'John'. 3. Field 'Last Name' is optional. Enter 'Doe'. 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Leave empty. 7. Field 'Gender' is mandatory. Select 'Others'. 8. Field 'Other Gender' is mandatory when 'Others' selected. Enter 'Nonbinary'. 9. Change 'Gender' selection to 'Female'. 10. Verify 'Other Gender' field is hidden, cleared, and no validation error triggered. 11. Submit form and verify success."
