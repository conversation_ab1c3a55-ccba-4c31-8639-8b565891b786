Test_Case_ID,Test_Case_Description,First_Name,Last_Name,Date_of_Birth,Phone_Number,Email,Website,Expected_Result,Error_Message,Test_Case_Category,Test_Steps
TC001,Negative Path: Phone Number field left empty triggers error.,"""","""",25-Jun-2006,"""",<EMAIL>,"""",Failure,Enter a number for this field.,Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): leave empty. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: Error message 'Enter a number for this field.' displayed for Phone Number.
TC002,Negative Path: Phone Number contains letters triggers immediate error and submission error.,"""","""",25-Jun-2006,12345abc6789,<EMAIL>,"""",Failure,Enter only numbers.,Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): enter '12345abc6789' (contains letters). 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Observe immediate error 'Enter only numbers.' during typing in Phone Number field. 9. Attempt to submit form. Expected: Submission blocked with error 'Enter only numbers.' for Phone Number.
TC003,Negative Path: Phone Number exceeds maximum 15 digits triggers error on submission.,"""","""",25-Jun-2006,1234567890123456,<EMAIL>,"""",Failure,Maximum limit: 15 digits.,Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): enter '1234567890123456' (16 digits). 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: Error message 'Maximum limit: 15 digits.' displayed for Phone Number.
TC004,Negative Path: Date of Birth in the future triggers error.,"""","""",25-Jun-2025,1234567890123,<EMAIL>,"""",Failure,Enter a valid date.,Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2025' (future date). 5. Phone Number (mandatory): enter '1234567890123'. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: Error message 'Enter a valid date.' displayed for Date of Birth.
TC005,Negative Path: Date of Birth less than 18 years old triggers error.,"""","""",26-Jun-2006,1234567890123,<EMAIL>,"""",Failure,Enter a valid date.,Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '26-Jun-2006' (one day less than 18 years ago). 5. Phone Number (mandatory): enter '1234567890123'. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: Error message 'Enter a valid date.' displayed for Date of Birth.
TC006,Negative Path: First Name contains numbers triggers error.,John3,"""",25-Jun-2006,1234567890123,<EMAIL>,"""",Failure,Only letters are allowed.,Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): enter 'John3' (contains number). 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): enter '1234567890123'. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: Error message 'Only letters are allowed.' displayed for First Name.
TC007,Negative Path: Last Name contains symbols triggers error.,"""",Smith!,25-Jun-2006,1234567890123,<EMAIL>,"""",Failure,Only letters are allowed.,Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): enter 'Smith!' (contains symbol). 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): enter '1234567890123'. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: Error message 'Only letters are allowed.' displayed for Last Name.
TC008,Negative Path: First Name contains spaces triggers error.,John Doe,"""",25-Jun-2006,1234567890123,<EMAIL>,"""",Failure,Only letters are allowed.,Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): enter 'John Doe' (contains space). 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): enter '1234567890123'. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: Error message 'Only letters are allowed.' displayed for First Name.
TC009,Negative Path: Website missing www. triggers error.,"""","""",25-Jun-2006,1234567890123,<EMAIL>,example.com,Failure,Enter a valid website. (eg: www.domain.com),Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): enter '1234567890123'. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): enter 'example.com' (missing www.). 8. Submit the form. Expected: Error message 'Enter a valid website. (eg: www.domain.com)' displayed for Website.
TC010,Negative Path: Website starts with http:// but missing www. triggers error.,"""","""",25-Jun-2006,1234567890123,<EMAIL>,http://example.com,Failure,Enter a valid website. (eg: www.domain.com),Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): enter '1234567890123'. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): enter 'http://example.com' (starts with http:// but no www.). 8. Submit the form. Expected: Error message 'Enter a valid website. (eg: www.domain.com)' displayed for Website.
TC011,Negative Path: Website starts with www. but missing domain extension triggers error.,"""","""",25-Jun-2006,1234567890123,<EMAIL>,www.example,Failure,Enter a valid website. (eg: www.domain.com),Negative Path,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): enter '1234567890123'. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): enter 'www.example' (missing domain extension). 8. Submit the form. Expected: Error message 'Enter a valid website. (eg: www.domain.com)' displayed for Website.
TC012,Negative Path: All mandatory fields empty triggers multiple error messages.,"""","""","""","""","""","""",Failure,Enter a number for this field.;Enter a valid email address. (eg: <EMAIL>);Enter a valid date.,Negative Path,"1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): leave empty. 5. Phone Number (mandatory): leave empty. 6. Email (mandatory): leave empty. 7. Website (optional): leave empty. 8. Submit the form. Expected: Multiple error messages displayed: 'Enter a number for this field.' for Phone Number, 'Enter a valid email address. (eg: <EMAIL>)' for Email, and 'Enter a valid date.' for Date of Birth."
TC013,Edge Case: First Name contains hyphen triggers error.,John-Doe,"""",25-Jun-2006,1234567890123,<EMAIL>,"""",Failure,Only letters are allowed.,Edge Case,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): enter 'John-Doe' (contains hyphen). 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): enter '1234567890123'. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: Error message 'Only letters are allowed.' displayed for First Name.
TC014,Edge Case: First Name contains accented letter triggers error.,José,"""",25-Jun-2006,1234567890123,<EMAIL>,"""",Failure,Only letters are allowed.,Edge Case,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): enter 'José' (contains accented letter). 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006'. 5. Phone Number (mandatory): enter '1234567890123'. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: Error message 'Only letters are allowed.' displayed for First Name.
TC015,Edge Case: Date of Birth exactly 18 years ago accepted.,"""","""",25-Jun-2006,1234567890123,<EMAIL>,"""",Success,,Edge Case,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '25-Jun-2006' (exactly 18 years ago). 5. Phone Number (mandatory): enter '1234567890123'. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: Form submits successfully with no error messages.
TC016,Edge Case: Date of Birth one day short of 18 years rejected.,"""","""",26-Jun-2006,1234567890123,<EMAIL>,"""",Failure,Enter a valid date.,Edge Case,1. Navigate to https://emlabsform.onrender.com/. 2. First Name (optional): leave empty. 3. Last Name (optional): leave empty. 4. Date of Birth (mandatory): enter '26-Jun-2006' (one day less than 18 years ago). 5. Phone Number (mandatory): enter '1234567890123'. 6. Email (mandatory): enter '<EMAIL>'. 7. Website (optional): leave empty. 8. Submit the form. Expected: Error message 'Enter a valid date.' displayed for Date of Birth.
