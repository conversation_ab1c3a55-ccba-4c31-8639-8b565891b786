import { test, expect } from '@playwright/test';

test.describe('Website Field Validation – Blank Optional Fields', () => {
  const FORM_URL = 'https://emlabsform.onrender.com/';
  const WEBSITE_ERROR_TEXT = 'Enter a valid website. (eg: www.domain.com)';

  test('should show website format error when domain missing prefix', async ({ page }) => {
    // Step 1: Navigate to page
    try {
      await page.goto(FORM_URL, { waitUntil: 'load', timeout: 10_000 });
    } catch {
      throw new Error('Page failed to load');
    }
    await expect(page).toHaveURL(FORM_URL, { timeout: 10_000 });

    // Helper: verify presence of a labeled field, leave blank or fill value
    const prepareField = async (
      label: string,
      missingMsg: string,
      value?: string
    ) => {
      const locator = page.getByLabel(label);
      try {
        await locator.waitFor({ state: 'visible', timeout: 5_000 });
      } catch {
        throw new Error(missingMsg);
      }
      if (value !== undefined) {
        await locator.fill(value);
      }
    };

    // Step 2 & 3: Optional fields, leave blank
    await prepareField('First Name', 'First Name field missing');
    await prepareField('Last Name', 'Last Name field missing');

    // Step 4: Phone Number (mandatory)
    await prepareField('Phone Number', 'Phone Number field missing', '123456789');

    // Step 5: Email (mandatory)
    await prepareField('Email', 'Email field missing', '<EMAIL>');

    // Step 6: Date of Birth (mandatory)
    await prepareField('Date of Birth', 'Date of Birth field missing', '15-May-2007');

    // Step 7: Website (optional, fill with invalid format)
    await prepareField('Website', 'Website field missing', 'domain.com');

    // Step 8: Click Submit
    const submitBtn = page.getByRole('button', { name: 'Submit' });
    try {
      await submitBtn.waitFor({ state: 'visible', timeout: 10_000 });
    } catch {
      throw new Error('Submit button missing');
    }
    await submitBtn.click();

    // Step 9: Verify Website error message
    const websiteError = page.getByText(WEBSITE_ERROR_TEXT);
    try {
      await websiteError.waitFor({ state: 'visible', timeout: 10_000 });
    } catch {
      throw new Error('Website error message not displayed');
    }
    await expect(websiteError).toBeVisible();
  });
});