const { test, expect } = require('@playwright/test');

test('login test', async ({ page }) => {
  // Navigate to the login page
  await page.goto('https://freelance-learn-automation.vercel.app/login');
  await page.waitForTimeout(2000); // Wait for 2 seconds after page load

  // Fill in the login form
  await page.getByRole('textbox', { name: 'Enter Email' }).fill('<EMAIL>');
  await page.waitForTimeout(1000); // Wait for 1 second after entering email
  
  await page.getByRole('textbox', { name: 'Enter Password' }).fill('12345678');
  await page.waitForTimeout(1000); // Wait for 1 second after entering password

  // Click the sign in button
  await page.getByRole('button', { name: 'Sign in' }).click();
  await page.waitForTimeout(2000); // Wait for 2 seconds after clicking sign in

  // Verify successful login by checking URL and navigation elements
  await expect(page).toHaveURL('https://freelance-learn-automation.vercel.app/');
  await expect(page.getByRole('button', { name: 'Sign out' })).toBeVisible();
});
