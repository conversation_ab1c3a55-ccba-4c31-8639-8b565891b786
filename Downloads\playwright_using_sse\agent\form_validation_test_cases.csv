Test_Case_ID,Test_Case_Description,First_Name,Last_Name,Phone_Number,Email,Website,Expected_Result,Error_Message,Test_Case_Category,Test_Steps
TC001,Happy Path: All mandatory fields correctly filled with valid values; optional fields empty,"""","""",123456789,<EMAIL>,"""",Success,,Happy Path,"1. Navigate to the form URL. 2. Fill ""Phone Number"" (mandatory) with '123456789'. 3. Fill ""Email"" (mandatory) with '<EMAIL>'. 4. Fill ""Date of Birth"" (mandatory) with '19-May-2007' (exactly 18 years before 19-May-2025). 5. Leave ""First Name"" (optional) empty. 6. Leave ""Last Name"" (optional) empty. 7. Leave ""Website"" (optional) empty. 8. Submit the form. 9. Expect successful submission."
TC002,Happy Path: All fields correctly filled with valid values including optional fields with mixed case names and subdomain email,<PERSON><PERSON><PERSON>,JaneDOE,987654321,<EMAIL>,www.domain.com,Success,,Happy Path,"1. Navigate to the form URL. 2. Fill ""First Name"" (optional) with '<PERSON><PERSON><PERSON>'. 3. Fill ""Last Name"" (optional) with 'Jane<PERSON><PERSON>'. 4. Fill ""Phone Number"" (mandatory) with '987654321'. 5. Fill ""Email"" (mandatory) with '<EMAIL>'. 6. Fill ""Date of Birth"" (mandatory) with '19-May-2007' (exactly 18 years before 19-May-2025). 7. Fill ""Website"" (optional) with 'www.domain.com'. 8. Submit the form. 9. Expect successful submission."
TC003,"Negative Path: All required fields left blank triggering required field errors for Phone Number, Email, Date of Birth","""","""","""","""","""",Failure,Phone Number: Enter a number for this field.|Email: Enter a valid email address. (eg: <EMAIL>)|Date of Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.,Negative Path,"1. Navigate to the form URL. 2. Leave all fields empty. 3. Note that ""Phone Number"" (mandatory), ""Email"" (mandatory), and ""Date of Birth"" (mandatory) are left blank. 4. Submit the form. 5. Expect error messages: ""Phone Number: Enter a number for this field."", ""Email: Enter a valid email address. (eg: <EMAIL>)"", and ""Date of Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.""."
TC004,"Negative Path: Format validation errors for Phone Number (non-numeric), Email (invalid format and spaces), Website (invalid format)","""","""",12345abc,user @domain.com,http://domain,Failure,Phone Number: Enter only numbers.|Email: Enter a valid email address. (eg: <EMAIL>)|Website: Enter a valid website. (eg: www.domain.com),Negative Path,"1. Navigate to the form URL. 2. Fill ""Phone Number"" (mandatory) with '12345abc' (contains letters). 3. Fill ""Email"" (mandatory) with 'user @domain.com' (contains space). 4. Fill ""Website"" (optional) with 'http://domain' (starts with protocol but missing www.). 5. Fill ""Date of Birth"" (mandatory) with '19-May-2000' (valid date to isolate format errors). 6. Submit the form. 7. Expect error messages: ""Phone Number: Enter only numbers."", ""Email: Enter a valid email address. (eg: <EMAIL>)"", and ""Website: Enter a valid website. (eg: www.domain.com)""."
TC005,"Negative Path: Length and character restrictions errors for First Name and Last Name (numbers, symbols, spaces, accented letters, hyphens)",J0hn,Jane-Doe,123456789,<EMAIL>,www.domain.com,Failure,First Name: First name must contain alphabetic characters only.|Last Name: Last name must contain alphabetic characters only.,Negative Path,"1. Navigate to the form URL. 2. Fill ""First Name"" (optional) with 'J0hn' (contains number). 3. Fill ""Last Name"" (optional) with 'Jane-Doe' (contains hyphen). 4. Fill ""Phone Number"" (mandatory) with '123456789'. 5. Fill ""Email"" (mandatory) with '<EMAIL>'. 6. Fill ""Date of Birth"" (mandatory) with '19-May-2000' (valid date). 7. Fill ""Website"" (optional) with 'www.domain.com'. 8. Submit the form. 9. Expect error messages: ""First Name: First name must contain alphabetic characters only."", ""Last Name: Last name must contain alphabetic characters only.""."
TC006,Negative Path: Date of Birth validation errors for future date and underage (less than 18 years),"""","""",123456789,<EMAIL>,www.domain.com,Failure,Date of Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.,Negative Path,"1. Navigate to the form URL. 2. Fill ""Date of Birth"" (mandatory) with '20-May-2007' (one day short of 18 years from 19-May-2025). 3. Fill ""Phone Number"" (mandatory) with '123456789'. 4. Fill ""Email"" (mandatory) with '<EMAIL>'. 5. Fill ""Website"" (optional) with 'www.domain.com'. 6. Submit the form. 7. Expect error message: ""Date of Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.""."
TC007,"Edge Case: Boundary test for Date of Birth exactly 18 years ago accepted, and one day short rejected","""","""",123456789,<EMAIL>,www.domain.com,Failure,Date of Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.,Edge Case,"1. Navigate to the form URL. 2. Fill ""Date of Birth"" (mandatory) with '20-May-2007' (one day short of 18 years). 3. Fill ""Phone Number"" (mandatory) with '123456789'. 4. Fill ""Email"" (mandatory) with '<EMAIL>'. 5. Fill ""Website"" (optional) with 'www.domain.com'. 6. Submit the form. 7. Expect error message: ""Date of Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18."". 8. Repeat steps 2-7 with ""Date of Birth"" as '19-May-2007' (exactly 18 years) and expect success."
TC010,"Edge Case: Validate accepted ages 20, 50, 75 years old for Date of Birth","""","""",123456789,<EMAIL>,www.domain.com,Success,,Edge Case,"1. Navigate to the form URL. 2. Fill ""Phone Number"" (mandatory) with '123456789'. 3. Fill ""Email"" (mandatory) with '<EMAIL>'. 4. Fill ""Website"" (optional) with 'www.domain.com'. 5. Fill ""Date of Birth"" (mandatory) with '19-May-2005' (20 years old). Submit and expect success. 6. Repeat with '19-May-1975' (50 years old) and '19-May-1950' (75 years old), expect success each time."
