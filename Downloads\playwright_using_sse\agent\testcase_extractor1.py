import requests
from requests.exceptions import RequestException
from langchain_openai import ChatOpenAI  
from langchain_core.prompts import ChatPromptTemplate
import os
import io
import re
import logging
from typing import Optional, Dict, Any
from dotenv import load_dotenv
import pandas as pd
from datetime import datetime
import json
from openai import OpenAI
# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
load_dotenv()
def extract_doc_id(url: str) -> Optional[str]:
    """Extract Google Doc ID from URL using regex pattern matching."""
    patterns = [
        r'https://docs.google.com/document/d/([a-zA-Z0-9_-]+)', 
        r'https://drive.google.com/open\?id=([a-zA-Z0-9_-]+)'    
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    
    logger.error(f"Could not extract document ID from URL: {url}")
    return None

def generate_system_prompt() -> str:
    """
    Generates a system prompt for an advanced test case generator designed for form validation systems.
 
    The generated prompt describes the process of analyzing form validation requirements and converting them into structured test cases in CSV format. These test cases are suitable for QA automation or manual testing. The form fields, their validation rules, and acceptance criteria will be provided by the user. The method outlines the steps to translate these requirements into test cases covering all possible scenarios, including happy paths, negative paths, and edge cases.
 
    The prompt specifies the format and structure of the CSV output, detailing the columns required and the type of data each should contain. It also includes guidelines for dynamic field calculations, such as date manipulations and interdependencies between fields. The output must be a well-formed CSV file, ready for direct use in test management tools.
 
    Returns:
        str: A formatted system prompt string ready for use by the test case generator.
    """
 
    current_date=datetime.now().strftime("%d-%b-%Y")
    SYSTEM_PROMPT = """
    You are an advanced test case generator for form validation systems. Your job is to analyze form validation requirements and generate minimal, comprehensive, and logically grouped test cases in clean CSV format suitable for both QA automation and manual testing. The user will provide form fields, their validation rules, and acceptance criteria. Your task is to drastically limit test cases by aggressively combining related scenarios and strictly limiting same-type validation checks to one test case per validation type across all applicable fields. Group fields with shared validation types or error conditions into single tests, ensuring comprehensive coverage with the absolute fewest test cases possible. Avoid overloading with test cases by prioritizing shared conditions and eliminating redundant scenarios.
    
    When form validation requirements are provided:
    1. Analyze and Extract:
    - List all form fields and preserve their input order
    - Identify:
    - Required vs optional fields
    - All validation types (e.g., required, length, format, range)
    - Error messages per field per rule
    - Field dependencies and relationships (e.g., cross-field validation)
    - Values, rules, and edge conditions tied to the current date (e.g., age or date validation using {{current_date}})
    
    2. Test Design and Grouping Logic:
    - Happy Path:
    - One test case using valid values for every field
    - Results in successful submission
    - Demonstrates baseline successful behavior
    - Negative Path:
    - Strictly limit to one test case per validation type across all fields:
        - All required fields left blank → one test
        - All fields with format validation errors (e.g., invalid email, phone) → one test for all fields with similar format rules
        - All fields with length validation errors (e.g., too long) → one test for all fields with identical length limits
        - All fields with range validation errors (e.g., out-of-range age) → one test for all fields with similar range rules
    - Group fields that:
        - Share the same validation type (e.g., all fields with required check, all fields with max length 50) or error message (e.g., "Invalid format")
        - Produce identical error conditions for the same validation type
        - Have no dependency conflicts
    - For cross-field validations, combine all fields with the same validation type (e.g., date comparisons) into one test case
    - Use one representative value to cover multiple scenarios (e.g., one invalid email format for all email fields)
    - Describe common actions once, applying to all relevant fields
    - Edge Cases:
    - Strictly limit to one test case per validation type across all fields:
        - One test for each boundary condition (e.g., max+1 for all fields with same max length)
        - Combine fields with identical boundaries (e.g., max length for all text fields)
    - Include date-specific cases (e.g., leap years, min/max age) using {{current_date}} in a single test for date-related validations
    - Only group edge cases with shared validation types
    - Reuse step descriptions
    - Redundancy Avoidance:
    - Drastically limit test cases by combining related scenarios and restricting same-type validation checks to one test per validation type
    - Merge scenarios with shared validation types, error conditions, or boundaries
    - Test each validation type exactly once across all fields, unless cross-field dependencies require otherwise
    - Reject test cases that don’t add unique validation coverage
    - Aim for minimal test cases: one happy path, one negative path per validation type, one edge case per validation type
    - Justify any additional test cases due to unique dependencies
    
    3. Output CSV Structure:
    Your final output must be a CSV table with the following columns:
    - Test_Case_ID: Sequential ID (TC001, TC002, etc.)
    - Test_Case_Description: Clear purpose of the test (include logic for dynamic or grouped values)
    - [Non-dynamic Field Columns]: One column per form field (use _ for spaces), preserve field order, exclude date of birth
    - Expected_Result: "Success" or "Failure"
    - Error_Message: Pipe (|) separated messages like Field1: Required|Field2: Invalid Format
    - Test_Case_Category: Happy Path, Negative Path, or Edge Case
    - Test_Steps:
    - Start with the test form’s URL (provided in input)
    - Provide step-by-step instructions
    - For each field mentioned in a step, explicitly state: "["Field_Name"] is [mandatory/optional]" using the exact field name in quotes
    - For grouped validations, describe the common pattern once, then list applicable fields with their "["Field_Name"] is [mandatory/optional]" status
    - Show calculation logic for dynamic values (e.g., DOB from {{current_date}})
    - Specify error messages
    - Ensure steps are clear for both human and AI agents
    
    4. General Guidelines:
    - Use "" for blank values
    - Use dd-MMM-yyyy for all date fields
    - Use {{current_date}} for dynamic date logic
    - Use realistic, representative values
    - Drastically limit test cases by:
    - Combining fields with shared validation types or error messages into single tests
    - Restricting same-type validation checks to one test per validation type
    - Testing dependent fields together
    - Using one test per boundary condition across similar fields
    - Parameterizing values to cover multiple scenarios
    - Avoid duplicate test logic or redundant cases
    - Test each validation type exactly once, unless cross-field logic requires otherwise
    - Ensure comprehensive coverage with the minimal number of test cases
    
    
    5. Output CSV Format and Escaping Rules:
    - Enclose every field in double quotes (e.g., "value")
    - Escape double quotes inside a field with two double quotes (e.g., "Click the ""Submit"" button")
    - Do not break rows with extra commas or line breaks inside fields
    - Use only comma (,) as the delimiter
    - Match the exact number of columns in the header row
    - Properly enclose fields with commas, line breaks, or quotes to preserve CSV integrity
    """
    
 
    SYSTEM_PROMPT = SYSTEM_PROMPT.replace("{{current_date}}", current_date)
    return SYSTEM_PROMPT


def get_text_from_google_doc(url: str) -> Optional[str]:
    """Retrieve text content from a Google Doc using its URL."""
    doc_id = extract_doc_id(url)
    if not doc_id:
        raise ValueError("Invalid Google Doc URL format")
    
    export_url = f'https://docs.google.com/document/d/{doc_id}/export?format=txt'
    try:
        response = requests.get(export_url, timeout=10)
        response.raise_for_status() 
        return response.text
    except RequestException as e:
        logger.error(f"Error fetching Google Doc: {e}")
        raise

def update_test_case_ids(df):
    df['Test_Case_ID'] = [f'TC{str(i+1).zfill(3)}' for i in range(len(df))]
    return df

def show_test_cases(df):
    print(f"\nTotal rows: {len(df)}")
    for i, row in enumerate(df.itertuples(), start=1):
        print(f"Test case {i}: {row.Test_Case_ID} - {row.Test_Case_Description}")

def ask_llm_for_action(command, total):
    system_prompt = f"""
You are an AI assistant helping a user manage a table of test cases stored in a CSV file. The CSV has {total} test cases and includes at least these columns:
- Test_Case_ID (e.g., TC001, TC002, ...)
- Test_Case_Description

The user will give short commands like:
- "remove test case 4"
- "delete test cases 2-5"
- "remove 3, 5 and 7"
- "approved"
- "remove 4 and approve"

Your job:
- Return only a **JSON object** with these fields:
  {{
    "remove": [list of test case row numbers to remove, 1-based],
    "approved": true or false
  }}

Rules:
- If the user says "approved", set `"approved": true`
- If the user gives numbers or ranges, set them under `"remove"` as a list of integers
- If they say both (e.g. "remove 2, 3 and approve"), return both
- If the command is unclear, return:
  {{ "remove": [], "approved": false }}

Examples:
- "delete 5" → {{ "remove": [5], "approved": false }}
- "remove 1, 4, 6" → {{ "remove": [1, 4, 6], "approved": false }}
- "remove 3-5 and approve" → {{ "remove": [3, 4, 5], "approved": true }}
- "approved" → {{ "remove": [], "approved": true }}

DO NOT include explanations, markdown, comments, reasoning, or summaries.
Your response must ONLY be valid JSON.
"""

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": command}
    ]

    client = OpenAI(
        base_url="https://openrouter.ai/api/v1",
        api_key=os.environ.get("OPENROUTER_API_KEY"),
    )

    response = client.chat.completions.create(
        model="google/gemini-2.0-flash-001",
        messages=messages
    )

    raw=response.choices[0].message.content.strip()
    match = re.search(r"```json\s*(\{.*?\})\s*```", raw, re.DOTALL)
    cleaned = match.group(1) if match else raw

    return cleaned





def generate_test_cases_csv(form_spec: str, output_filename: str = "test_cases.csv") -> pd.DataFrame:
    api_key = os.environ.get("OPENAI_API_KEY")
    if not api_key:
        raise ValueError("Please set a valid OpenAI API key in environment variables")

    try:
        logger.info("Initializing ChatOpenAI model")
        chat = ChatOpenAI(
            model="gpt-4.1-mini-2025-04-14", 
            temperature=0.3,
            openai_api_key=api_key,
        )

        prompt = ChatPromptTemplate.from_messages([
            ("system", generate_system_prompt()),
            ("human", "{user_input}")
        ])

        chain = prompt | chat

        logger.info("Generating test cases...")
        response = chain.invoke({"user_input": form_spec})

        csv_string = response.content if hasattr(response, 'content') else str(response)

        if not csv_string.strip() or ',' not in csv_string:
            logger.error("Invalid CSV response received")
            raise ValueError("Model did not return valid CSV content")

        csv_buffer = io.StringIO(csv_string)
        df = pd.read_csv(csv_buffer)

        if len(df.columns) < 3:  
            logger.warning("Generated CSV may be incomplete - fewer columns than expected")

      
        done = False
        while not done:
            show_test_cases(df)
            user_command = input("\nNeeds Approval: ")

            llm_response = ask_llm_for_action(user_command, len(df))
            print(f"\n LLM response: {llm_response}")

            try:
                result = json.loads(llm_response)
                to_remove = result.get("remove", [])
                approved = result.get("approved", False)
                for idx in sorted(to_remove, reverse=True):
                    if 1 <= idx <= len(df):
                        df = df.drop(index=idx - 1).reset_index(drop=True)
                if to_remove:
                    print(f"✅ Removed test case(s): {to_remove}")

                # Only update IDs and save when approved
                if approved:
                    df = update_test_case_ids(df)
                    df.to_csv(output_filename, index=False)
                    print(f"Approved test cases saved to '{output_filename}' with updated IDs.")
                    done = True
            except Exception as e:
                logger.error(f" Failed to parse LLM response: {e}")

    except Exception as e:
        logger.error(f"Error generating test cases: {e}")
        raise

    return df