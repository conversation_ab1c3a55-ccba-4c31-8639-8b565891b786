<!DOCTYPE html>
<html>
<head>
  <title>Test Agent Extension</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      background-color: #1e2a44;
      color: #ffffff;
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
      min-height: 100vh;
      margin: 0;
      padding: 0;
    }

    .main-container {
      min-height: 100vh;
      max-width: 500px;
      background-color: #1e2a44;
    }

    .header-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: #ffffff;
      margin-bottom: 1.25rem;
    }

    .results-container {
      background-color: #2a3b5a;
      border-radius: 8px;
      font-size: 0.875rem;
      line-height: 1.5;
      min-height: 500px;
      max-height: 400px;
      overflow-y: auto;
    }

    /* Custom scrollbar styling */
    .results-container::-webkit-scrollbar {
      width: 6px;
    }

    .results-container::-webkit-scrollbar-track {
      background: #2a3b5a;
    }

    .results-container::-webkit-scrollbar-thumb {
      background: #4b5a7a;
      border-radius: 3px;
    }

    .results-container h3 {
      font-size: 1rem;
      color: #ffffff;
      margin-bottom: 0.625rem;
    }

    .results-container p {
      color: #d1d5db;
      margin-bottom: 0.3125rem;
    }

    .results-container table {
      color: #d1d5db;
    }

    .results-container th {
      background-color: #3b4a6b;
      color: #ffffff;
      border-color: #4b5a7a;
    }

    .results-container td {
      border-color: #4b5a7a;
    }

    .results-container ul {
      list-style-type: none;
      padding-left: 0;
    }

    .results-container li {
      color: #d1d5db;
      margin-bottom: 0.3125rem;
    }

    .error {
      color: #f87171;
    }

    .results-container pre {
      background-color: #1e2a44;
      color: #d1d5db;
      border-radius: 6px;
      font-size: 0.8125rem;
    }

    .results-container code {
      color: #d1d5db;
    }

    .status-text {
      font-size: 0.875rem;
      font-style: italic;
      color: #a3bffa;
    }

    .search-input-group {
      background-color: #2a3b5a;
      border-radius: 8px;
      border: none;
    }

    .search-input-group .form-control {
      background: transparent;
      border: none;
      color: #ffffff;
      font-size: 0.875rem;
    }

    .search-input-group .form-control:focus {
      background: transparent;
      border: none;
      box-shadow: none;
      color: #ffffff;
    }

    .search-input-group .form-control::placeholder {
      color: #a3bffa;
      opacity: 0.7;
    }

    .search-icon {
      background-color: #2a3b5a;
      border: none;
      color: #a3bffa;
      font-size: 1.125rem;
    }

    .submit-btn {
      background-color: #6366f1;
      border-color: #6366f1;
      font-size: 0.875rem;
      transition: background-color 0.2s;
    }

    .submit-btn:hover {
      background-color: #5457d6;
      border-color: #5457d6;
    }

    .submit-btn:disabled {
      background-color: #4b5a7a;
      border-color: #4b5a7a;
    }

    /* Responsive adjustments */
    @media (max-width: 576px) {
      .main-container {
        max-width: 100%;
      }
      
      .header-title {
        font-size: 1.25rem;
      }
      
      .results-container {
        min-height: 500px;
        max-height: 350px;
      }
    }

    @media (min-width: 768px) {
      .main-container {
        max-width: 600px;
      }
      
      .results-container {
        min-height: 500px;
        max-height: 450px;
      }
    }

    @media (min-width: 992px) {
      .main-container {
        max-width: 700px;
      }
      
      .results-container {
        min-height: 400px;
        max-height: 500px;
      }
    }
  </style>
</head>
<body>
  <div class="container-fluid">
    <div class="row justify-content-center">
      <div class="col-12 main-container">
        <div class="p-3 p-md-4">
          <!-- Header -->
          <div class="mb-3">
            <h1 class="header-title">Test Agent</h1>
          </div>

          <!-- Results Container -->
          <div id="results" class="results-container p-3 mb-3">
            <!-- Results will be populated here -->
          </div>

          <!-- Status -->
          <div id="status" class="status-text mb-3">
            Waiting for input...
          </div>

          <!-- Input Area -->
          <div class="input-group search-input-group">
            <span class="input-group-text search-icon">
            </span>
            <input 
              type="text" 
              id="commandInput" 
              class="form-control" 
              placeholder="Type 'generate test cases'..."
            />
            <button 
              id="submitButton" 
              class="btn btn-primary submit-btn px-3"
              type="button"
            >
              Submit
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script src="popup1.js"></script>

</body>
</html>