import os
import tkinter as tk
from tkinter import filedialog, messagebox
import docx2txt
import numpy as np
from sentence_transformers import SentenceTransformer
import chromadb
from PyPDF2 import PdfReader

# Initialize global variables
root = None
search_entry = None
results_text = None
status_label = None
model = None
collection = None

def initialize_vector_db():
    """Initialize the embedding model and ChromaDB"""
    global model, collection
    
    # Initialize embedding model
    model = SentenceTransformer('all-MiniLM-L6-v2')
    
    # Initialize ChromaDB
    chroma_client = chromadb.Client()
    try:
        # Try to get existing collection
        collection = chroma_client.get_collection(name="documents")
    except:
        # Create new collection if it doesn't exist
        collection = chroma_client.create_collection(name="documents")
    
    return model, collection

def extract_text_from_document(file_path):
    """Extract text from a document based on its file type"""
    file_extension = os.path.splitext(file_path)[1].lower()
    
    if file_extension in ['.docx', '.doc']:
        return docx2txt.process(file_path)
    elif file_extension == '.pdf':
        reader = PdfReader(file_path)
        text = ""
        for page in reader.pages:
            text += page.extract_text()
        return text
    else:
        raise ValueError("Unsupported file format")

def chunk_text(text):
    """Split text into manageable chunks"""
    # Basic chunking (split by paragraphs)
    chunks = [chunk.strip() for chunk in text.split('\n\n') if chunk.strip()]
    return chunks

def generate_embeddings(chunks):
    """Generate embeddings for text chunks"""
    global model
    embeddings = model.encode(chunks)
    return embeddings

def store_in_vector_db(chunks, embeddings, document_id):
    """Store chunks and embeddings in the vector database"""
    global collection
    
    # Add chunks with their embeddings
    for i, (chunk, embedding) in enumerate(zip(chunks, embeddings)):
        collection.add(
            documents=[chunk],
            embeddings=[embedding.tolist()],
            metadatas=[{"source": document_id, "chunk_id": i}],
            ids=[f"{document_id}_chunk_{i}"]
        )
    
    return len(chunks)

def upload_document():
    """Handle document upload and processing"""
    global status_label, results_text, root
    
    file_path = filedialog.askopenfilename(
        title="Select Document",
        filetypes=[("Document Files", "*.docx *.doc *.pdf"), ("All Files", "*.*")]
    )
    
    if not file_path:
        return
    
    try:
        status_label.config(text=f"Processing {os.path.basename(file_path)}...")
        root.update()
        
        # Extract text from document
        text = extract_text_from_document(file_path)
        
        # Split into chunks
        chunks = chunk_text(text)
        
        # Generate embeddings
        embeddings = generate_embeddings(chunks)
        
        # Store in vector database
        document_id = os.path.basename(file_path)
        chunk_count = store_in_vector_db(chunks, embeddings, document_id)
        
        status_label.config(text=f"Added {chunk_count} chunks from {os.path.basename(file_path)}")
        messagebox.showinfo("Success", f"Document processed and stored in vector database")
        
        # Show sample in the results
        results_text.delete(1.0, tk.END)
        results_text.insert(tk.END, f"Document: {document_id}\n\n")
        results_text.insert(tk.END, f"First few chunks:\n")
        for i, chunk in enumerate(chunks[:3]):
            if i >= 3:
                break
            results_text.insert(tk.END, f"\nChunk {i+1}:\n{chunk[:200]}...\n")
            
        if len(chunks) > 3:
            results_text.insert(tk.END, f"\n... plus {len(chunks) - 3} more chunks")
            
    except Exception as e:
        messagebox.showerror("Error", f"Failed to process document: {str(e)}")
        print(f"Error: {str(e)}")
        status_label.config(text="Error processing document")

def search_documents():
    """Search for documents using semantic similarity"""
    global search_entry, results_text, status_label, model, collection
    
    query = search_entry.get().strip()
    if not query:
        messagebox.showinfo("Info", "Please enter a search query")
        return
    
    try:
        # Generate query embedding
        query_embedding = model.encode(query).tolist()
        
        # Search in ChromaDB
        results = collection.query(
            query_embeddings=[query_embedding],
            n_results=5
        )
        
        # Display results
        results_text.delete(1.0, tk.END)
        
        if not results["documents"][0]:
            results_text.insert(tk.END, "No results found.")
            return
            
        results_text.insert(tk.END, f"Search Results for: '{query}'\n\n")
        
        for i, (doc, metadata) in enumerate(zip(results["documents"][0], results["metadatas"][0])):
            source = metadata.get("source", "Unknown")
            results_text.insert(tk.END, f"Match {i+1} (from {source}):\n")
            results_text.insert(tk.END, f"{doc[:300]}...\n\n")
            
        status_label.config(text=f"Found {len(results['documents'][0])} results")
        
    except Exception as e:
        messagebox.showerror("Error", f"Search failed: {str(e)}")
        status_label.config(text="Search error")

def create_ui():
    """Create the user interface"""
    global root, search_entry, results_text, status_label
    
    # Initialize main window
    root = tk.Tk()
    root.title("Document to Vector Database")
    root.geometry("600x400")
    root.configure(bg="#f0f0f0")
    
    # Main frame
    main_frame = tk.Frame(root, bg="#f0f0f0")
    main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
    
    # Title label
    title_label = tk.Label(
        main_frame, 
        text="Document Vector Database", 
        font=("Arial", 16, "bold"),
        bg="#f0f0f0"
    )
    title_label.pack(pady=10)
    
    # Description
    desc_label = tk.Label(
        main_frame,
        text="Upload Word documents or PDFs to vectorize and store them",
        font=("Arial", 10),
        bg="#f0f0f0",
        wraplength=500
    )
    desc_label.pack(pady=10)
    
    # Upload button
    upload_btn = tk.Button(
        main_frame,
        text="Upload Document",
        command=upload_document,
        font=("Arial", 11),
        bg="#4CAF50",
        fg="white",
        padx=20,
        pady=10
    )
    upload_btn.pack(pady=20)
    
    # Search frame
    search_frame = tk.Frame(main_frame, bg="#f0f0f0")
    search_frame.pack(fill=tk.X, pady=20)
    
    search_label = tk.Label(
        search_frame,
        text="Search:",
        font=("Arial", 11),
        bg="#f0f0f0"
    )
    search_label.pack(side=tk.LEFT, padx=(0, 10))
    
    search_entry = tk.Entry(search_frame, width=40, font=("Arial", 11))
    search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
    
    search_btn = tk.Button(
        search_frame,
        text="Search",
        command=search_documents,
        font=("Arial", 11),
        bg="#2196F3",
        fg="white",
        padx=10
    )
    search_btn.pack(side=tk.LEFT, padx=10)
    
    # Results frame
    results_frame = tk.Frame(main_frame, bg="#f0f0f0")
    results_frame.pack(fill=tk.BOTH, expand=True, pady=10)
    
    results_text = tk.Text(results_frame, height=10, width=60, font=("Arial", 10))
    results_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    scrollbar = tk.Scrollbar(results_frame, command=results_text.yview)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    results_text.config(yscrollcommand=scrollbar.set)
    
    # Status label
    status_label = tk.Label(
        main_frame,
        text="Ready",
        font=("Arial", 10, "italic"),
        bg="#f0f0f0",
        fg="#555555"
    )
    status_label.pack(pady=10)
    
    return root

def main():
    """Main function to run the application"""
    # Check required packages
    try:
        import docx2txt
        import numpy
        import sentence_transformers
        import chromadb
        import PyPDF2
    except ImportError as e:
        missing_package = str(e).split("'")[1]
        print(f"Missing required package: {missing_package}")
        print("Please install required packages using:")
        print("pip install docx2txt sentence-transformers chromadb PyPDF2")
        exit(1)
    
    # Initialize vector database and model
    initialize_vector_db()
    
    # Create and start UI
    app_root = create_ui()
    app_root.mainloop()

if __name__ == "__main__":
    main()