import pandas as pd
from openai import OpenAI
import os
from dotenv import load_dotenv
import testcase_extractor

# --- CONFIGURATION ---
input_file = "form_validation_test_cases.csv"                # Your input CSV
input_column = "Test_Steps"             # Name of the column containing the original step
output_column = "Test_Steps_AI"   # Name of the column to store the AI-ready steps for test systems
output_file = "ai_ready_test_steps.csv" # Output CSV

load_dotenv()

# --- PROMPT TEMPLATE ---
def generate_prompt(step_text):
    return f"""
        You are an expert QA engineer tasked with designing reliable, detailed test steps for an LLM-based test runner that interacts with a web application using an accessibility tree, prioritizing visible text (e.g., field labels, button text) as the primary locator. Your goal is to create precise, actionable, and self-contained test instructions that strictly adhere to the provided step description, formatted as human-readable, numbered steps (e.g., 1., 2., 3.) for both human understanding and AI execution. Follow the process below, performing reasoning, planning, and self-correction internally to ensure accuracy and completeness, but output only the final test steps. Never invent additional steps or validations beyond what is explicitly provided..
 
        ---
 
        **Input**:  
        ### Original Step:  
        {step_text}
 
        ---
 
        Internal Process:
 
        Step 1: Reason About the Goal
 
        Analyze the original step description to understand its intent and purpose.
 
        Identify the core objective (e.g., form input, button click, verification) and map each provided step to a specific action.
 
        Consider ambiguities or missing details, inferring minimal assumptions only to resolve unclear locators or UI interactions, prioritizing the accessibility tree and text-based locators.
 
        Summarize the goal internally to ensure alignment with the input, avoiding any extrapolation beyond the provided steps.
 
        Step 2: Plan the Actions
 
        Translate each provided step into a specific action for the LLM test runner, using visible text locators such as:
 
        Field labels (e.g., "First Name" for an input field).
 
        Button or link text (e.g., "Submit" for a button).
 
        Visible text in messages (e.g., "error" for verification).
 
        Use accessibility tree attributes (e.g., role="textbox", aria-label) as fallbacks only if text locators are ambiguous (e.g., duplicate labels).
 
        For each action, consider:
 
        Navigation: Use exact URLs or paths provided.
 
        Input: Enter exact values specified, or verify fields are empty if instructed.
 
        Clicks: Target buttons by their visible text.
 
        Verifications: Check only what is explicitly requested (e.g., absence of error messages).
 
        Include wait conditions where logical to account for:
 
        Page loading (e.g., after navigation).
 
        Element availability (e.g., before input or click).
 
        UI updates (e.g., after form submission for message checks).
 
        Anticipate challenges (e.g., slow-loading pages, duplicate labels) and plan mitigations (e.g., wait conditions, role-based fallbacks), but do not add actions beyond the input steps.
 
        List actions internally in the exact order of the provided steps.
 
        Step 3: Self-Correct and Validate
 
        Review the planned actions to ensure they:
 
        Match the provided steps exactly, with no added or omitted actions.
 
        Use visible text locators wherever possible, with accessibility tree fallbacks only when necessary.
 
 
        Check for edge cases (e.g., missing labels, hidden elements) and ensure error handling covers these scenarios.
 
        Confirm that no unrequested validations (e.g., checking for success messages when only error absence is specified) are included.
 
        Adjust the plan to align strictly with the input if discrepancies are found.
 
        Step 4: Generate Final Test Steps
 
        Create detailed, self-contained test instructions formatted as numbered steps (e.g., 1., 2., 3.), including:
 
        Action: e.g., navigate, enter text, click, verify.
 
        Locator: Prioritize visible text (e.g., "label 'First Name'", "button 'Submit'"); use role or ARIA as fallback (e.g., "role 'textbox' with aria-label 'Email'") only if text is insufficient.
 
        Value (if applicable): e.g., "<EMAIL>", or "empty" for verification.
 
        Expected Outcome (for verifications): e.g., "verify no text 'error' is visible".
 
        Error Handling: e.g., "if not found, fail with message 'Element missing'".
 
        Ensure steps are concise, unambiguous, and follow the exact sequence of the input.
 
        Add wait conditions where logical:
 
        After navigation: wait 10 seconds for page/form visibility.
 
        After UI changes: wait 10 seconds for message visibility or page updates.
 
        Use language that is human-readable but precise for AI execution (e.g., "Enter '<EMAIL>' in field labeled 'Email'").
 
        Output Format:
 
        Final Test Steps:
 
        [Action] [value] in/using [locator]; [wait condition]; [expected outcome]; [error handling].
 
        [Action] [value] in/using [locator]; [wait condition]; [expected outcome]; [error handling].
 
        ...
 
        Guidelines:
 
        Use clear, technical language optimized for an LLM test runner querying an accessibility tree.
 
        Prioritize visible text locators (e.g., "label 'First Name'", "button 'Submit'").
 
        When describing each test step, clearly indicate whether a field is mandatory or optional using a consistent, descriptive phrase. Normalize all representations of field requirements into one of the following formats:
 
            -Field Name is mandatory
           
            -Field Name is optional
           
            -This applies regardless of how the field requirement is originally written. For example:
           
            -Convert FirstName(Mandatory) → First Name is mandatory
           
            -Convert PhoneNumber mandatory → Phone Number is mandatory
           
            -Convert email(optional) → Email is optional
           
           
        Split camelCase and PascalCase for better readability.
        Example: FirstName → First Name, userEmailAddress → User Email Address
           
            -Interpret symbols or shorthand:
           
            -Asterisk * often means "required"
           
            -Words like opt, req, reqd, mand are normalized
           
            -fieldname - req → Field Name is mandatory
           
            -fieldname (opt) → Field Name is optional
           
            -Ignore placeholder text or field labels unless they explicitly declare the requirement.
 
 
        Use accessibility tree attributes (e.g., role, aria-label) only as fallbacks if text locators are ambiguous or duplicate (e.g., two fields labeled "Name").
 
        Avoid CSS or XPath locators (e.g., #id, //input).
 
        Strictly adhere to the provided steps, never adding unrequested actions or validations (e.g., do not check for success messages unless explicitly stated).
 
        If the input is vague (e.g., unclear field names), assume standard form elements with matching text labels, and use the exact text provided (e.g., "First Name" for a field).
 
        Include error handling for each step (e.g., "if not found, fail with message 'Element missing'").
 
        Add wait conditions where logical:
 
        After navigation: wait 10 seconds for page load.
 
        After actions triggering UI changes: wait 10 seconds for verifications.
 
        Use reasonable wait durations (e.g., 5 seconds for elements, 10 seconds for page load or UI updates).
 
        Output only the Final Test Steps section, formatted as numbered, human-readable steps, in the exact order of the input.
 
        Example Application:
 
        Input:
 
        Original Step:
 
        Navigate to "/login"
 
        Enter "testuser" in username field
 
        Enter "password123" in password field
 
        Click login button
 
        Verify no error message appears
 
        Output:
 
        Final Test Steps:
 
        1. Navigate to page "/login"; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message "Login page failed to load".
 
        2. Verify no text containing "error" or "invalid" is visible; wait up to 10 seconds for page to stabilize; verify no error messages are present; if found, fail with message "Error message detected after login"."""

def call_llm(prompt):
    try:
        client = OpenAI(
            base_url="https://openrouter.ai/api/v1",
            api_key=os.environ.get("OPENROUTER_API_KEY"),  
        )
        
        if not os.environ.get("OPENROUTER_API_KEY"):
            raise ValueError("API key is missing. Please set the 'OPENROUTER_API_KEY' environment variable.")
        
        completion = client.chat.completions.create(
            model="deepseek/deepseek-r1-distill-llama-70b:free",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.3,
        )
        
        message_content = completion.choices[0].message.content

        return message_content
    except Exception as e:
        return f"Error: {str(e)}"

# --- EXTRACT TEXT AFTER FINAL STEPS ---
def extract_text_after_final_steps(text):
    marker = "Final Test Steps:"
    if marker in text:
        return text.split(marker, 1)[1].strip() 
    return text.strip()

# --- PROCESS ROW ---
def process_row(row):
    
    """
    Process a single row of a DataFrame containing test cases.

    Parameters:
        row (pd.Series): Row of the DataFrame to process.

    Returns:
        str: Final test steps as a string.

    """

    original_step = row[input_column]
    prompt = generate_prompt(original_step)
    llm_response = call_llm(prompt)
    final_steps_after_text = extract_text_after_final_steps(llm_response)
    return final_steps_after_text

def filter_by_expected_result(df: pd.DataFrame, no: int = None, type: str = ""):
    """
    Filters a DataFrame:
    - If 'Regression' is in type: return all rows.
    - Else: filter by Expected_Result and optionally limit the number of rows.

    Parameters:
        df (pd.DataFrame): DataFrame to filter.
        expected_value (str): Value to filter in 'Expected_Result' column.
        no (int, optional): Max number of rows to return (ignored for Regression).
        type (str): Test case type.

    Returns:
        pd.DataFrame: Filtered DataFrame.
    """
    if "Regression" in type:
        return df
    filtered = df[df['Test_Case_Category'] == "Happy Path"]
    if no is not None:
        return filtered.head(no)
    return filtered

# def check():
#     print("haiiiiii ==========================================================================")
# url = 'https://docs.google.com/document/d/1FJGQMGvB_G42gaY5jDFZjMK41mx-65uXWbgTaNrG-hI/edit?tab=t.0'
# testcase_extractor.generate_test_cases_csv(testcase_extractor.get_text_from_google_doc(url), "form_validation_test_cases.csv")
# df = pd.read_csv(input_file)
# filtered_df = filter_by_expected_result(df,no=2, type="Edge Case") # tyep can be "Success", "Failure", or "Regression" to get all rows
# testcase_extractor.logger.info(f"Processing {len(filtered_df)} rows...")
# filtered_df[output_column] = filtered_df.apply(process_row, axis=1)

# filtered_df.to_csv(output_file, index=False)
# testcase_extractor.logger.info(f"✅ Test cases saved to {output_file}")

