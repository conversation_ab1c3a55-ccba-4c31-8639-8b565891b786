import streamlit as st
import asyncio
from dotenv import load_dotenv
import os
import re
import subprocess
import time
import openai
from mcp_use import MCPAgent, MCPClient
import os
import sys
from fastapi import FastAPI, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from langchain_openai import ChatOpenAI
from openai import OpenAI
import pandas as pd
from pydantic import BaseModel
from typing import List, Dict, Optional
import logging
from testcase_extractor import *
from llm_testcase_generator import *
from port_finder import *
# Placeholder for the DataFrame
from pathlib import Path
from server_run import run_mcp_in_background
import platform
from streamlit.components.v1 import html
from retriever import ask_playwright_docs
# Load environment variables
load_dotenv()
import   pandas as pd
from documentation_fetcher import  process_csv_data    
openai.api_key = os.getenv("OPENAI_API_KEY")
import google.generativeai as genai  
import json
import uuid
import sys
import psycopg2
from datetime import datetime
import pandas as pd
from db.projectfiles import get_project_file_url
from db.testcases import fetch_testcases
from db.teststeps import insert_test_step,fetch_existing_teststep_ids
# config_file = {
#   "mcpServers": {
#     "playwright": {
#       "command": "npx",
#       "args": [
#         "@playwright/mcp@latest",
#         "--save-trace",
#         "--output-dir=./output"
#       ]
#     }
#   }
# }
 
config_file = "mcp-playwright.json"
directory = os.getcwd()
is_windows = sys.platform.startswith("win")
run_mcp_in_background(port=8931)
 
# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
 
# Load environment variables
load_dotenv()
 
# Input model for the API request
class TestRequest(BaseModel):
    url: str
    message: str

class TypeScriptScript(BaseModel):
    test_case_id: str
    test_description: str
    typescript_code: str
 
class TestResponse(BaseModel):
    status: str
    test_cases: Optional[List[Dict[str, Any]]] = None
    detailed_test_steps: Optional[List[Dict[str, Any]]] = None
    typescript_scripts: Optional[List[TypeScriptScript]] = None
    errors: Optional[List[str]] = None
    processing_times: Optional[Dict[str, float]] = None


# In-memory state to mimic Streamlit session_state
state = {
    "test_cases_requested": False,
    "detailed_steps_requested": False,
    "automation_requested": False,
    "session_state_info": {"success": [], "dataframe": None},
    "intermediate_test": None,
    "test_cases_ready": False
}
 
# Initialize FastAPI app
app = FastAPI(title="Test Agent API")
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins (less secure, use for debugging)
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*"],
)

 
 
# system_prompt = """
# You are a QA testing assistant powered by Playwright via MCP.
 
# You will be given a Playwright documentation guide with tested patterns and code snippets. Use this guide to inform your approach—reference its examples, follow its structure, and adapt them to the specific user request. When in doubt, refer to the documentation and prefer partial matches only if they are unique and reliable.
 
# When a user provides a URL and a specific field to validate (e.g., "first name"), follow these steps:
 
# 1. Navigate to the provided URL.
 
# 2. Locate the field using only real accessibility information:
#    - Use only actual content from the page's accessibility tree.
#    - Prefer exact `label` text, `placeholder`, `role` + accessible name, or unique visible text.
#    - Never guess or fabricate selectors.
#    - Avoid dynamic IDs, auto-generated classes, or CSS selectors unless no accessible option exists.
 
# 3. After entering test input:
#    - Submit the form.
#    - Observe any UI feedback, such as validation messages, highlights, or alerts.
 
# 4. Log for each test:
#    - The test input
#    - Expected behavior
#    - Actual observed behavior
#    - Validation or error message (if any)
 
# 5. After all inputs are tested:
#    - Summarize results clearly under labeled sections (e.g., Test 1: Empty Input)
#    - Use headings, spacing, and structure for readability.
#    - Include a "Playwright Test Code" section at the end with the full, reusable code used.
 
# 6. Then, generate a clean, grouped test report:
#    - Each block must include:
#      * Test title (e.g., "Missing Required Field")
#      * Status (PASSED / FAILED)
#      * Observations or messages
#      * Screenshot (if relevant)
#      * Code snippet used (optional)
 
# 7. Provide a summary of the test results, highlighting any failures or issues found.
 
# Selector Guidelines:
# - Only use what’s actually present in the DOM’s accessibility tree
# - Always confirm labels, placeholders, or roles before using them
# - Use partial matches only when the match is unique and consistent
# - Avoid unnecessary selector chaining
 
# Validation Handling:
# - Confirm visibility of messages using `expect(locator).toBeVisible()`
# - Confirm content using `expect(locator).toContainText(...)`
# - Avoid fragile or promise-based patterns like `.textContent()`
 
# Your goal is to generate accurate, accessibility-first tests that mirror what a real user would experience.
# """
 
 
def build_system_prompt() -> str:
    system_prompt = (
        "You are an expert Playwright Test Automation Engineer specialized in automation."
        "An expert at converting written test steps into clean, maintainable, and efficient code."
        "You don’t just read text literally—you grasp its underlying semantic meaning"
        "Your mission is to generate robust, maintainable test suites that leverage Playwright's Model-Centered Protocol (MCP). "
        "CRITICAL: MCP utilizes the accessibility tree rather than the full DOM - all element interactions must use "
        "accessibility attributes instead of traditional DOM structure.\n\n"
 
        "<core_capabilities>\n"
        "- Create production-ready Playwright test suites in TypeScript\n"
        "- Work exclusively with accessibility tree attributes (not DOM)\n"
        "- Generate self-contained, executable test code with proper assertions\n"
        "- Implement comprehensive error handling and recovery mechanisms\n"
        "- Produce clear test documentation with rationales for implementation choices\n"
        "</core_capabilities>\n\n"
 
        "<locator_strategy>\n"
        "PREFERRED (use in this order):\n"
        "1. `getByLabel`: Target elements by their associated label text\n"
        "2. `getByRole`: Target elements by their ARIA role with appropriate options\n"
        "3. `getByText`: Target elements by their visible text content\n"
        "4. `getByPlaceholder`: Target input fields by placeholder text\n"
        "AVOID THESE (use only as documented last resort):\n"
        "- CSS selectors, XPath, ID selectors, or any DOM structure-dependent locators\n"
        "- If you must use a non-accessibility locator, document the justification\n"
        "</locator_strategy>\n\n"
 
 
        "<reasoning_steps>\n"
        "Step 1: Reason About the Goal\n"
        "  - Analyze the user's step descriptions to identify the intent.\n"
        "  - Determine the core action (input, click, verify) and desired outcome.\n"
        "  - If locator info is missing or ambiguous, infer only what's necessary using accessibility principles.\n"
        "  - Do not invent new behaviors or extend beyond the provided instructions.\n\n"
        "Step 2: Contextually dynamic fields-When interpreting test steps that involve form inputs, recognize that some fields are dynamic in a **real-world sense**, meaning their valid values depend on business logic, temporal context, or test intent — not just DOM structure."
 
        "Follow these guidelines:"
       
        "1. **Respect Business Rules**:"
        "   - For age restrictions (e.g., must be 18+), compute the correct date relative to today's date."
        "   - For fields like expiration dates, use future dates unless the test requests otherwise."
       
        "2. **Use realistic, synthetic data**:"
        "   - Names, emails, phone numbers, etc., should be plausible and locale-appropriate."
        "   - Invalid values should be intentionally incorrect but syntactically relevant"
       
        "Examples:"
       
        "Test Step: 'Enter a valid date of birth for a user who is at least 18 years old."
        "find current date minus 18 years add to the date of birth field"
        "Step 3: Semantic reasoning -Proper semantic understanding is crucial for ensuring accurate test execution, especially when using LLM-driven test frameworks. The following gives semantic misunderstandings "
        "   1. **'Empty' Inputs**:"
        "       - Interpret 'empty' as an empty string: `""` (i.e., leave the field blank)."
        "       - Do NOT type the word 'empty'."
           
        "   2. **'Visible' vs. 'Hidden'**:"
        "       - 'Visible' means the element should be rendered and visible on screen."
        "       - 'Hidden' refers to elements not visible, either via `display: none` or `visibility: hidden`."
        "Step 4: Plan the Actions\n"
        "  - Break each goal into sequenced Playwright actions.\n"
        "  - Prefer visible text and ARIA-based locators.\n"
        "  - Include:\n"
        "    * Navigations (to pages or forms)\n"
        "    * Inputs (values for fields with labels)\n"
        "    * Actions (clicks on accessible buttons)\n"
        "    * Verifications (assertions on state or message visibility)\n"
        "  - Insert waits for navigation, element visibility, or interaction readiness.\n"
        "  - Handle challenges (e.g., duplicate labels, async rendering) using fallback strategies.\n\n"
        "Step 5: Self-Correct and Validate\n"
        "  - Review action plan for alignment with input steps.\n"
        "  - Ensure only the intended validations are present.\n"
        "  - Avoid overchecking (e.g., asserting success when only error is expected).\n"
        "  - Consider edge cases (missing/hidden labels, race conditions).\n"
        "  - Adjust to align with accessibility constraints and test determinism.\n\n"
        "Step 6: Generate Code (Playwright + TypeScript)\n"
        "  - Use `test.describe`, `test.beforeEach`, and multiple `test()` blocks.\n"
        "  - Use `await expect()` with meaningful selectors and accessible text.\n"
        "  - Structure test files cleanly with fixtures and helper utilities if needed.\n"
        "  - Name tests clearly (e.g., 'should show error for invalid email').\n"
        "  - Include comments, typed inputs, and properly formatted assertions.\n"
        "  - Ensure code is fully standalone and executable.\n"
        "  - Take screenshot and save to output directory.\n"
        "  - Save the test code to the output directory.\n"
       
        "</reasoning_steps>\n\n"
 
        "<testing_methodology>\n"
        "1. Requirement Analysis\n"
        "   - Extract clear test objectives from requirements\n"
        "   - Identify critical user flows and validation points\n"
        "   - Consider accessibility implications in test design\n\n"
        "2. Test Action Planning\n"
        "   - Design clear test step sequences with accessibility-first approach\n"
        "   - Anticipate potential stability issues and plan mitigations\n"
        "   - Structure tests for readability and maintenance\n\n"
        "3. Implementation Best Practices\n"
        "   - Implement page objects or component abstractions when beneficial\n"
        "   - Use descriptive test and function names that reflect business logic\n"
        "   - Include appropriate waits and synchronization points\n"
        "   - Add comprehensive assertions that validate both state and content\n"
        "   - Implement error recovery mechanisms for fragile interactions\n\n"
        "4. Validation Strategy\n"
        "   - Form field validation (empty, invalid, valid inputs)\n"
        "   - Error message verification via accessibility attributes\n"
        "   - For fields do not use exact text match, use partial matches or regex where appropriate\n"
        "   - Visual validation where appropriate\n"
        "5. Guidelines:\n"
        "   -Use the tool named <browser_generate_playwright_test> to generate test cases.\n"
        "   -Do not use promises or response checks unless explicitly specified.\n"
        "   -Refrain from adding any extra validations that are not explicitly stated in the test steps.\n"
        "   -Avoid capturing unnecessary details from the website that are not outlined in the test steps.\n"
        "   -Use timeout/wait in milliseconds example 5000,10000\n"
        "6. When verifying dynamic text content (e.g., error messages, validation hints, or status updates), always ensure the target element is scrolled into view using scrollIntoViewIfNeeded() or equivalent. This guarantees visibility for both the automation tool and visual validation.\n"
        "</testing_methodology>\n\n"
 
        "<complex_scenarios_handling>\n"
        "- Dynamic content: Implement `waitForSelector` with appropriate timeout and state options\n"
        "- Shadow DOM: Use special frame handling capabilities in Playwright\n"
        "- iframes: Leverage `frameLocator()` with accessibility selectors\n"
        "- SPAs: Add stabilization waits and state verification before actions\n"
        "- Multi-step forms: Implement progressive form filling with validation at each step\n"
        "- Internationalization: Create parameterized tests that work across locales\n"
        "</complex_scenarios_handling>\n\n"
 
        "<code_structure>\n"
        "- Test files should follow a consistent organization pattern\n"
        "- Include setup/teardown with appropriate fixture management\n"
        "- Implement reusable helper functions for common operations\n"
        "- Use appropriate test annotations and metadata\n"
        "- Follow TypeScript best practices with proper typing\n"
        "- Implement appropriate error handling with diagnostic information\n"
        "</code_structure>\n\n"
 
        "<output_format>\n"
        "1. Test Strategy Summary (concise overview of approach)\n"
        "2. Playwright Test Code (complete, runnable TypeScript code)\n"
        "3. Implementation Notes (key design decisions, accessibility considerations)\n"
        "4. Potential Enhancements (suggestions for improved coverage or robustness)\n"
        "5. Finally save the code in a file named playwright.spec.ts\n"
        "</output_format>\n\n"
 
        "<communication_style>\n"
        "- Use formal, professional language appropriate for technical documentation\n"
        "- Present code with clear formatting and proper syntax highlighting\n"
        "- When explaining code choices, focus on technical rationale and best practices\n"
        "- Highlight accessibility considerations in your implementation decisions\n"
        "- Provide complete context for code decisions rather than fragmented explanations\n"
        "</communication_style>\n"
       
        "<reference>\n"
        "-Use the provided Playwright documentation as a reference for best practices and code snippets .\n"
        " -Use the test cases as a source of truth for the test steps and expected behavior.\n"
        " -No additional checks should be performed other than the ones mentioned in the test steps.\n"
        "- Do not invent new behaviors or extend beyond the provided instructions.\n"
        "- Test cases are well written so no need to add any extra validations that are not explicitly stated in the test steps.\n"
    )
    return system_prompt
   
def add_instructions(test_step_docuemtation):
    instruction=(
f"Here are the test steps and documentation {test_step_docuemtation} for Playwright MCP to convert to optimal automation script.Give priority to the test steps and refer to the documentation only as a guide. Documentation may add extra information that is not needed for the test steps. Do not add any extra validations that are not explicitly stated in the test steps. Do not invent new behaviors or extend beyond the provided instructions. Test cases are well written so no need to add any extra validations that are not explicitly stated in the test steps.Specifically any text validations out of scope of the test steps should not be added. Use the documentation only as a guide and not as a source of truth for the test steps. Do not add any extra validations that are not explicitly stated in the test steps. Do not invent new behaviors or extend beyond the provided instructions. Test cases are well written so no need to add any extra validations that are not explicitly stated in the test steps.")
    return instruction
 
client = MCPClient.from_config_file(config_file)
llm = ChatOpenAI(
    model="o4-mini-2025-04-16",
    temperature=1,
    reasoning_effort="low",
)
agent = MCPAgent(
    llm=llm,
    client=client,
    max_steps=30,
    memory_enabled=True,
    system_prompt=build_system_prompt()
)
 
 
 
def extract_typescript_code(response_text):
    """
    Extract the TypeScript code block from the response text that appears after any heading containing 'Playwright Test Code'.
    Args:
        response_text (str): The response text containing the Playwright test code.
    Returns:
        str: The extracted TypeScript code or None if not found.
    """
   
    pattern = r'Playwright Test Code.*?```(?:typescript|javascript|ts|js)\s*(.*?)```'
    match = re.search(pattern, response_text, re.DOTALL | re.IGNORECASE)
    if match:
        extract_code =match.group(1).strip()
       
        return extract_code
   
    return None
 
 
 
def open_playwright_report(directory=directory, is_windows=True):
    """
    Launch the Playwright report in a web browser.
    Args:
        directory (str): The working directory to run the command.
        is_windows (bool): Whether the system is Windows (default: True).
    Returns:
        str: Success message or error message.
    """
    try:
 
        # First check port 9323 with all methods
        logger.info("\nChecking port 9323 with all methods:")
        is_port_in_use(9323)
       
        # Then find next available port
        logger.info("\nFinding available port:")
        port = find_available_port(9323)
        logger.info(f"Final port to use: {port}")
       
        command = ["npx", "playwright", "show-report", f"--port={port}"]
 
        # Prepare startupinfo for Windows to hide the console window (optional)
        startupinfo = None
        if is_windows:
            startupinfo = subprocess.STARTUPINFO()
            startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW
        with st.spinner("Generating Test Report..."):
            if is_windows:
                subprocess.Popen(
                    f'start cmd /c {" ".join(command)}',
                    cwd=directory,
                    shell=True,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    startupinfo=startupinfo
                )
            else:
                subprocess.Popen(
                    command,
                    cwd=directory,
                    stdout=subprocess.DEVNULL,
                    stderr=subprocess.DEVNULL,
                    start_new_session=True
                )
            return f"Report launched successfully on port {port}."
    except Exception as e:
        return f"Error showing report: {str(e)}"
 
 
 
def save_and_run_ts_code(code,index):
    """
    Save the extracted TypeScript code to a file and execute it using Playwright.
    Args:
        code (str): The TypeScript code to be saved and run.
    Returns:
        str: The result of the test execution.
    """
    if not code:
        return "No TypeScript code found in the response."
   
    try:
       
        with open(f"{directory}/tests/test_report{index+1}.spec.ts", "w", encoding="utf-8") as f:
            f.write(code)
 
    except Exception as e:
       
        return f"Error saving TypeScript file: {str(e)}"
   
    # try:
    #     command = [
    #         "npx",
    #         "playwright",
    #         "test",
    #         f"tests/test_report{index+1}.spec.ts",
    #         "--headed"
    #     ]
 
    #     # Step 2: Run the test in headed mode using Popen
    #     process = subprocess.Popen(
    #         command,
    #         cwd=directory,
    #         stdout=subprocess.PIPE,
    #         stderr=subprocess.PIPE,
    #         text=True,
    #         encoding="utf-8",
    #         shell=is_windows  # Only use shell=True on Windows to ensure PATH resolution
    #     )
 
    #     try:
    #         # Capture the output and errors
    #         stdout, stderr = process.communicate(timeout=15)
    #         if process.returncode != 0:
    #             return f"Test failed with error: {stderr.strip()}"
 
    #         report_status = open_playwright_report(directory, is_windows=is_windows)
    #         print(f"Report Status: {report_status}")
           
    #         output = ""
    #         output += f"\n{report_status}"
    #         output += f"\n{stdout.strip()}"
           
    #         return output
 
    #     except subprocess.TimeoutExpired:
    #         process.kill()
    #         return "Test execution timed out after 60 seconds."
       
    # except Exception as e:
    #     return f"Error executing test: {str(e)}"
 
 
 
 
def count_ts_files(directory):
 
    return len(list(Path(directory).glob('*.ts')))
 
# Set folder path
 
def  check_ts_code_count(df):
   
    folder_path = Path('tests/')
 
    # Compare counts
 
    ts_file_count = count_ts_files(folder_path)
 
    df_row_count = len(df)
   
   
    logger.info(f"TS file count: {ts_file_count}")
 
    logger.info(f"DataFrame row count: {df_row_count}")
   
    if ts_file_count == df_row_count:
       
        logger.info("✅ Counts match.")
        return True
 
    else:
 
        logger.info("❌ Counts do not match.")
        return False
 
def check_test_code_count():
    df = pd.read_csv('ai_ready_test_steps.csv')
    return check_ts_code_count(df)
   
import subprocess
import platform
import streamlit as st
 
def run_playwright_tests(index):
    test_directory = f"tests/test_report{index}.spec.ts"
    is_windows = platform.system() == "Windows"
 
    command = ["npx", "playwright", "test", test_directory, "--config=playwright.config.ts"]
 
 
    try:
        result = subprocess.run(
            command,
            cwd=".",  # make sure this is where your config file is
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            shell=is_windows
        )
        return result.stdout, result.stderr
    except Exception as e:
        st.write(f"Error running subprocess: {e}")
        return "", str(e)

# Function to get LLM intent using Gemini
def get_llm_intent(user_input):
    user_intent_identification ="""You are an intelligent QA assistant. Analyze the user's request.

    1.  Identify the primary action requested by the user from the following options:
        *   Generate form validation test cases (use action "test_cases")
        *   Generate detailed test steps (use action "detailed_steps")
        *   Generate Playwright automation scripts (use action "automation")

    2.  If the user's request includes a URL (e.g., starting with http:// or https://), extract this URL.

    3.  If the user's request is clear about the action, respond with a JSON object.
        *   The JSON object MUST contain an "action" key with one of the determined actions ("test_cases", "detailed_steps", or "automation").
        *   If a URL was identified in the user's request, the JSON object MUST also contain a "url" key with the extracted URL as its value.

    4.  If the user's request is ambiguous regarding the action, ask:
        "Would you like to generate only test cases, detailed test steps, or full automation scripts?"
        In this ambiguous case, do NOT return JSON.

    Here are examples of expected JSON output:
    {"action": "test_cases"}
    {"action": "detailed_steps", "url": "https://example.com/login"}
    {"action": "automation", "url": "http://localhost:8080/signup"}

    ---
    User query:
    """
    prompt_to_send = f"{user_intent_identification}\n{user_input}"

    try:
        model = genai.GenerativeModel('gemini-1.5-flash-latest')
        generation_config = {
            "response_mime_type": "application/json",
        }
        response = model.generate_content(
            prompt_to_send,
            generation_config=generation_config
        )
        content = response.text
        content = content.strip()
        if content.startswith("```json"):
            content = content[len("```json"):].strip()
            if content.endswith("```"):
                content = content[:-len("```")].strip()

        return content

    except Exception as e:
        st.error(f"Error calling Gemini API: {e}")
        # Return an error message or default response if API call fails
        return f"Error: Could not get response from Gemini API ({e})"

def clean_playwright_output(raw_output: str) -> str:
    # Remove ANSI escape sequences
    ansi_escape = re.compile(r'\x1B\[[0-?]*[ -/]*[@-~]')
    cleaned = ansi_escape.sub('', raw_output)
    # Replace misencoded characters like â€º
    cleaned = cleaned.replace("â€º", "›")  # optional: replace with intended character

    lines = cleaned.splitlines()
    filtered_lines = [
        line for line in lines
        if "To open last HTML report run" not in line
        and "npx playwright show-report" not in line
        and line.strip() != ''
    ]
    # Or strip all non-ASCII if you prefer
    # cleaned = re.sub(r'[^\x00-\x7F]+','', cleaned)
    return "\n".join(filtered_lines)

def count_total_tokens(system_prompt: str, user_prompt: str,assistant_response: str = "",model: str = "gpt-4o"):
    # enc = tiktoken.encoding_for_model("cl100k_base")
    enc = tiktoken.get_encoding("cl100k_base")

    # This matches how OpenAI structures chat messages for GPT models
    tokens = 0
    tokens += 4  # each message adds 4 tokens overhead (role + message wrapper)
    tokens += len(enc.encode(system_prompt))
    tokens += 4
    tokens += len(enc.encode(user_prompt))
    tokens += 2  # every reply includes priming for assistant (e.g., <|start|>assistant...)

    return tokens
def count_total_output_tokens(assistant_response: str = "",model: str = "gpt-4o"):
    # enc = tiktoken.encoding_for_model("cl100k_base")
    enc = tiktoken.get_encoding("cl100k_base")
    tokens=0
    if assistant_response:
        tokens += 4  # overhead for message
        tokens += len(enc.encode(assistant_response))

    tokens += 2  # priming for assistant reply
    return tokens
def calculate_o4_mini_cost(input_tokens, output_tokens):
    cost = (input_tokens * 4 + output_tokens * 16) / 1_000_000
    return round(cost, 6)

def insert_test_cases_to_db(df:pd.DataFrame) -> None:
    """
    Insert test cases and steps into the PostgreSQL database based on schema.sql.

    Args:
        df (pd.DataFrame): DataFrame containing test cases.
        db_config (dict): Dictionary with keys: dbname, user, password, host, port.
    """
    project_id = str(uuid.uuid4())
    link_id = str(uuid.uuid4())

    try:
        conn = psycopg2.connect(
        dbname="QATOOL",
        user="postgres",
        password= os.getenv("DB_PASSWORD"),
        host="localhost",
        port="5432"
        )
        cursor = conn.cursor()

        # Insert dummy Project
        cursor.execute("""
            INSERT INTO Projects (project_id, project_name, description,created_at)
            VALUES (%s, %s, %s, %s)
        """, (project_id, "Dummy Project", "Test project for LLM-generated test cases",datetime.now()))

        # Insert dummy ProjectLink
        cursor.execute("""
            INSERT INTO ProjectLinks (link_id, project_id, url, page_title,scraped_at)
            VALUES (%s, %s, %s, %s, %s)
        """, (link_id, project_id, "https://dummyform.com", "Dummy Form",datetime.now()))

        # Insert TestCases and TestSteps
        for index, row in df.iterrows():
            testcase_id = str(uuid.uuid4())
            name = row.get("Test_Case_Description", f"Test Case {index+1}")
            description = row.get("Test_Steps", "no description")

            cursor.execute("""
            INSERT INTO TestCases (testcase_id, project_id, link_id, name, description,created_at)
            VALUES (%s, %s, %s, %s, %s, %s)
        """, (testcase_id, project_id, link_id, name, description,datetime.now()))

        conn.commit()

    except Exception as e:
        print(f"❌ Error inserting test cases: {e}")
        if conn:
            conn.rollback()
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()
        print("✅ Test cases successfully inserted.")


   
@app.get("/")
async def root():
    return {"message": "Vehicle Sales API is running"}
# API endpoint to generate test cases and scripts
@app.post("/generate-tests", response_model=TestResponse)
async def generate_tests(request: TestRequest):
    errors = []
    test_cases = []
    detailed_test_steps = []
    typescript_scripts = []
    processing_times = {}

    try:
        # Step 1: Parse LLM intent from user message
        logger.info(f"Processing user message: {request.message}")
        print(request.message)
        llm_response = get_llm_intent(request.message)
        try:
            parsed = json.loads(llm_response)
            action = parsed.get("action")
            url = parsed.get("url", request.url)
            print(action)
            
            # Set flags based on intent
            if action == "test_cases":
                state["test_cases_requested"] = True
                logger.info("Test Cases Requested")
            elif action == "detailed_steps":
                state["test_cases_requested"] = True
                state["detailed_steps_requested"] = True
                logger.info("Detailed Steps Requested")
            elif action == "automation":
                state["test_cases_requested"] = True
                state["detailed_steps_requested"] = True
                state["automation_requested"] = True
                logger.info("Automation Requested")
        except json.JSONDecodeError:
            errors.append(f"Invalid LLM response format: {llm_response}")
            return TestResponse(status="error", errors=errors)
        except Exception as e:
            errors.append(f"Error processing LLM intent: {str(e)}")
            return TestResponse(status="error", errors=errors)
        # Step 2: Process QA spec and generate test cases if requested
        if state["test_cases_requested"]:
            start_time_qa_spec = time.time()
            logger.info(f"Processing Google Doc URL: {url}")
            extractor_test_flag = get_text_from_google_doc(url)
            # Assuming check_test_code_count is defined; otherwise, remove or implement
            cout_checker = True  # Placeholder; implement if needed

            if not extractor_test_flag or not cout_checker:
                start_time_creating_testcase = time.time()
                logger.info("Generating new test cases from QA spec")
                # reasoning_steps = ""
                # gen = stream_test_case_generation(get_text_from_qaspec())
                # try:
                #     while True:
                #         token = next(gen)
                #         cleaned_token = re.sub(r"^\[Step\s*\d+\]\s*", "", token)
                #         reasoning_steps += cleaned_token + "\n"
                # except StopIteration as e:
                #     full_output = e.value
                #     try:
                #         df = save_csv_from_output(full_output, 'form_validation_test_cases.csv')

                #         df1=pd.read_csv("form_validation_test_cases.csv")
                #         insert_test_cases_to_db(df1)
                #         test_cases = df.to_dict(orient='records')
                #         state["session_state_info"]["dataframe"] = df
                #     except Exception as err:
                #         errors.append(f"Error extracting CSV: {str(err)}")
                testcase_extractor.generate_test_cases_csv(testcase_extractor.get_text_from_qaspec(), "form_validation_test_cases.csv")
                end_time_creating_testcase = time.time()
                duration_creating_testcase = end_time_creating_testcase - start_time_creating_testcase
                processing_times["test_case_generation"] = duration_creating_testcase
                state["session_state_info"]["success"].append(
                    f"Test case generation completed in {duration_creating_testcase:.2f} seconds"
                )

            end_time_qa_spec = time.time()
            duration_qa_spec = end_time_qa_spec - start_time_qa_spec
            processing_times["qa_spec_processing"] = duration_qa_spec
            state["session_state_info"]["success"].append(
                f"QA Spec processing completed in {duration_qa_spec:.2f} seconds"
            )

        # Step 3: Generate detailed test steps if requested
        if state["detailed_steps_requested"]:
            start_time_cases_generated = time.time()
            try:
                # df = pd.read_csv('form_validation_test_cases.csv')
                # df.columns = df.columns.str.strip()
                # filtered_df = filter_by_expected_result(df, no=None, type="Regression")
                # logger.info(f"Processing {len(filtered_df)} test cases")
                # filtered_df = filtered_df.iloc[0:1] # Limit to 1 for demo, adjust as needed
                # filtered_df['Test_Steps_AI'] = filtered_df.apply(process_row, axis=1)
                testcases = fetch_testcases("Form Testing", "https://form2-4y5z.onrender.com/")
                existing_ids = fetch_existing_teststep_ids("Form Testing", "https://form2-4y5z.onrender.com/")
                for tc in testcases[:1]:
                    if tc['testcase_id'] not in existing_ids:
                        output = process_row(tc)
                        insert_test_step(tc['testcase_id'], output)
                    else:
                        print(f"⏩ Skipped: Test step already exists for test case '{tc['name']}'")

                intermediate_test = filtered_df[['Test_Case_ID', 'Test_Case_Description', 'Test_Steps_AI']].rename(columns={
                    'Test_Case_ID': 'No',
                    'Test_Case_Description': 'Test Description',
                    'Test_Steps_AI': 'Detailed Test Steps'
                }).reset_index(drop=True)
                state["intermediate_test"] = intermediate_test
                detailed_test_steps = intermediate_test.to_dict(orient='records')
                intermediate_test.to_csv('ai_ready_test_steps.csv', index=False)
                state["test_cases_ready"] = True
            except Exception as e:
                errors.append(f"Error processing test cases: {str(e)}")

            end_time_cases_generated = time.time()
            duration_cases_generated = (end_time_cases_generated - start_time_cases_generated) / 60
            processing_times["detailed_test_steps"] = duration_cases_generated
            state["session_state_info"]["success"].append(
                f"Detailed test steps generation completed in {duration_cases_generated:.2f} minutes"
            )

        # Step 4: Generate automation scripts if requested
        if state["automation_requested"] and state["test_cases_ready"]:
            start_time_automation = time.time()
            try:
                prmpt = pd.read_csv('ai_ready_test_steps.csv')
                detailed_test_steps=[]
                for index, row in prmpt.iterrows():
                    logger.info(f"Generating automation scripts for test case no. : {index+1}")
                    test_steps = row['Detailed Test Steps']
                    docs = ask_playwright_docs(test_steps)
                    refine_prompt = f"{test_steps}\n\n{docs}"
                    references = add_instructions(refine_prompt)
                    response = await agent.run(f"\n\nUser Instructions:\n{references.strip()}")
                    total_tokens = count_total_tokens(build_system_prompt(),references.strip(), model="gpt-4.1-mini")
                    print("#################Estimated prompt token usage:", total_tokens)
                    output_tokens=count_total_output_tokens(response,model="gpt-4.1-mini")
                    print("output tokens",output_tokens)
                    cost=calculate_o4_mini_cost(total_tokens, output_tokens)
                    print ("######",cost)
                    code = extract_typescript_code(response)
                    test_cases=None
                    # step = {
                    #     "No": row["No"],
                    #     "Test Description": row["Test Description"]
                    # }
                    # detailed_test_steps.append(step)

                    # Save TypeScript code to file     

                    if code:
                        with open(f"tests/test_report{index+1}.spec.ts", "w", encoding="utf-8") as f:
                            f.write(code)
                        # Run Playwright tests
                        stdout_log, stderr_log = run_playwright_tests(index+1)
                        if stderr_log:
                            errors.append(f"Playwright test execution error: {stderr_log}")
                        # Open Playwright report
                        open_playwright_report()
                        cleaned_output=clean_playwright_output(stdout_log)
                        typescript_scripts.append({
                            'test_case_id': row['No'],
                            'test_description': row["Test Description"],
                            'typescript_code': cleaned_output
                        })
                        
                    else:
                        errors.append(f"No TypeScript code generated for test case {row['Test_Case_ID']}")

            except Exception as e:
                errors.append(f"Error generating TypeScript scripts: {str(e)}")

            end_time_automation = time.time()
            duration_automation = (end_time_automation - start_time_automation) / 60
            processing_times["automation"] = duration_automation
            state["session_state_info"]["success"].append(
                f"Automation scripts generation completed in {duration_automation:.2f} minutes"
            )

            # Save edited test cases to CSV (mimicking Streamlit's "Run Test" button)
            if state["intermediate_test"] is not None:
                start_time_save_csv = time.time()
                edited_df = state["intermediate_test"]
                edited_df["No"] = [f"TC{str(i+1).zfill(3)}" for i in range(len(edited_df))]
                edited_df.to_csv('ai_ready_test_steps.csv', index=False)
                logger.info(f"Test cases saved to ai_ready_test_steps.csv")
                end_time_save_csv = time.time()
                duration_save_csv = end_time_save_csv - start_time_save_csv
                processing_times["save_csv"] = duration_save_csv
                state["session_state_info"]["success"].append(
                    f"Detailed test steps saved to CSV in {duration_save_csv:.2f} seconds"
                )

        # Reset state for the next request
        state["test_cases_requested"] = False
        state["detailed_steps_requested"] = False
        state["automation_requested"] = False
        state["test_cases_ready"] = False

        # Step 5: Return response
        return TestResponse(
            status="success" if not errors else "partial_success",
            test_cases=test_cases if test_cases else None,
            detailed_test_steps=detailed_test_steps if detailed_test_steps else None,
            typescript_scripts=typescript_scripts if typescript_scripts else None,
            errors=errors if errors else None,
            processing_times=processing_times
        )

    except Exception as e:
        logger.error(f"API error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing request: {str(e)}")
 
# Run the FastAPI app (for local testing)
if __name__ == "__main__":
    import uvicorn
    uvicorn.run("TestAgent3:app", host="127.0.0.1", port=8001, reload=True)