import { test, expect } from '@playwright/test';

test.describe('Alphabetic-Only Validation for Names', () => {
  const FORM_URL = 'https://emlabsform.onrender.com/';
  const FIRST_NAME_ERROR = 'First name must contain alphabetic characters only.';
  const LAST_NAME_ERROR = 'Last name must contain alphabetic characters only.';

  test('should display alphabetic-only errors for accented-hyphen names', async ({ page }) => {
    // Step 1: Navigate to the form page
    try {
      await page.goto(FORM_URL, { waitUntil: 'load', timeout: 10_000 });
    } catch {
      throw new Error('Page failed to load');
    }
    await expect(page).toHaveURL(FORM_URL, { timeout: 10_000 });

    // Helper: wait for a labeled field and optionally fill it
    const fillOrLeave = async (
      label: string,
      missingMsg: string,
      value?: string
    ) => {
      const locator = page.getByLabel(label);
      try {
        await locator.waitFor({ state: 'visible', timeout: 5_000 });
      } catch {
        throw new Error(missingMsg);
      }
      if (value !== undefined) {
        await locator.fill(value);
      }
    };

    // Step 2: First Name (optional) – fill with accented/hyphenated value
    await fillOrLeave('First Name', 'First Name field missing', 'José-Marie');

    // Step 3: Last Name (optional) – fill with accented/hyphenated value
    await fillOrLeave('Last Name', 'Last Name field missing', 'Anne-Marie');

    // Step 4: Phone Number (mandatory)
    await fillOrLeave('Phone Number', 'Phone Number field missing', '123456789');

    // Step 5: Email (mandatory)
    await fillOrLeave('Email', 'Email field missing', '<EMAIL>');

    // Step 6: Date of Birth (mandatory)
    await fillOrLeave('Date of Birth', 'Date of Birth field missing', '15-May-2007');

    // Step 7: Website (optional)
    await fillOrLeave('Website', 'Website field missing', 'www.domain.com');

    // Step 8: Click Submit
    const submitBtn = page.getByRole('button', { name: 'Submit' });
    try {
      await submitBtn.waitFor({ state: 'visible', timeout: 10_000 });
    } catch {
      throw new Error('Submit button missing');
    }
    await submitBtn.click();

    // Step 9: Verify First Name error
    const firstNameError = page.getByText(FIRST_NAME_ERROR);
    try {
      await firstNameError.waitFor({ state: 'visible', timeout: 10_000 });
    } catch {
      throw new Error('First Name error not displayed');
    }
    await expect(firstNameError).toBeVisible();

    // Step 10: Verify Last Name error
    const lastNameError = page.getByText(LAST_NAME_ERROR);
    try {
      await lastNameError.waitFor({ state: 'visible', timeout: 10_000 });
    } catch {
      throw new Error('Last Name error not displayed');
    }
    await expect(lastNameError).toBeVisible();
  });
});