Test_Case_ID,Test_Case_Description,First_Name,Last_Name,Phone_Number,Email,Website,Expected_Result,Error_Message,Test_Case_Category,Test_Steps
TC001,"Happy Path: All mandatory fields correctly filled with valid data, optional fields empty","""","""",123456789,<EMAIL>,"""",<PERSON>,"""",Happy Path,"1. Navigate to the form URL. 2. Fill ""Phone Number"" (mandatory) with '123456789'. 3. Fill ""Email"" (mandatory) with '<EMAIL>'. 4. Fill ""Date of Birth"" (mandatory) with '19-May-2007' (exactly 18 years before 19-May-2025). 5. Leave ""First Name"" (optional) empty. 6. Leave ""Last Name"" (optional) empty. 7. Leave ""Website"" (optional) empty. 8. Submit the form. 9. Expect successful submission with no errors."
TC002,Happy Path: All fields correctly filled with valid data including optional names and website,<PERSON><PERSON><PERSON>,<PERSON>,987654321,<EMAIL>,www.domain.com,Success,"""",Happy Path,"1. Navigate to the form URL. 2. Fill ""First Name"" (optional) with 'JohnDOE'. 3. Fill ""Last Name"" (optional) with 'Smith'. 4. Fill ""Phone Number"" (mandatory) with '987654321'. 5. Fill ""Email"" (mandatory) with '<EMAIL>'. 6. Fill ""Date of Birth"" (mandatory) with '19-May-2007' (exactly 18 years before 19-May-2025). 7. Fill ""Website"" (optional) with 'www.domain.com'. 8. Submit the form. 9. Expect successful submission with no errors."
TC003,Negative Path: All required fields left blank to trigger required field errors,"""","""","""","""","""",Failure,Phone Number: Enter a number for this field.|Email: Enter a valid email address. (eg: <EMAIL>)|Date of Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.,Negative Path,"1. Navigate to the form URL. 2. Leave all fields blank. 3. Note that ""Phone Number"" (mandatory), ""Email"" (mandatory), and ""Date of Birth"" (mandatory) are blank. 4. Submit the form. 5. Expect errors: ""Phone Number: Enter a number for this field."", ""Email: Enter a valid email address. (eg: <EMAIL>)"", and ""Date of Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.""."
TC004,"Negative Path: Format validation errors for Phone Number, Email, Website, and Name fields with invalid characters or formats",John3,Smith!,123-abc,user @domain,http://domain,Failure,First Name: First name must contain alphabetic characters only.|Last Name: Last name must contain alphabetic characters only.|Phone Number: Enter only numbers.|Email: Enter a valid email address. (eg: <EMAIL>)|Website: Enter a valid website. (eg: www.domain.com),Negative Path,"1. Navigate to the form URL. 2. Fill ""First Name"" (optional) with 'John3' (contains number). 3. Fill ""Last Name"" (optional) with 'Smith!' (contains symbol). 4. Fill ""Phone Number"" (mandatory) with '123-abc' (contains letters and symbols). 5. Fill ""Email"" (mandatory) with 'user @domain' (contains space and missing valid domain). 6. Fill ""Website"" (optional) with 'http://domain' (starts with protocol but missing www.). 7. Fill ""Date of Birth"" (mandatory) with '19-May-2007' (valid). 8. Submit the form. 9. Expect errors for all invalid fields as specified."
TC005,Negative Path: Length and character restrictions on First Name and Last Name with spaces and accented letters,José ,Anne Marie,123456789,<EMAIL>,www.domain.com,Failure,First Name: First name must contain alphabetic characters only.|Last Name: Last name must contain alphabetic characters only.,Negative Path,"1. Navigate to the form URL. 2. Fill ""First Name"" (optional) with 'José ' (contains accented letter and trailing space). 3. Fill ""Last Name"" (optional) with 'Anne Marie' (contains space). 4. Fill ""Phone Number"" (mandatory) with '123456789'. 5. Fill ""Email"" (mandatory) with '<EMAIL>'. 6. Fill ""Date of Birth"" (mandatory) with '19-May-2007'. 7. Fill ""Website"" (optional) with 'www.domain.com'. 8. Submit the form. 9. Expect errors: ""First Name: First name must contain alphabetic characters only."", ""Last Name: Last name must contain alphabetic characters only.""."
TC006,Negative Path: Date of Birth invalid - future date and under 18 years old,"""","""",123456789,<EMAIL>,www.domain.com,Failure,Date of Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.,Negative Path,"1. Navigate to the form URL. 2. Fill ""Phone Number"" (mandatory) with '123456789'. 3. Fill ""Email"" (mandatory) with '<EMAIL>'. 4. Fill ""Date of Birth"" (mandatory) with '20-May-2007' (one day short of 18 years on 19-May-2025). 5. Leave ""First Name"" and ""Last Name"" empty. 6. Fill ""Website"" (optional) with 'www.domain.com'. 7. Submit the form. 8. Expect error: ""Date of Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.""."
TC007,Edge Case: Names with hyphens and accented characters rejected; leading/trailing spaces trigger errors, José,Anne-Marie,123456789,<EMAIL>,www.domain.com,Failure,First Name: First name must contain alphabetic characters only.|Last Name: Last name must contain alphabetic characters only.,Edge Case,"1. Navigate to the form URL. 2. Fill ""First Name"" (optional) with ' José' (leading space and accented letter). 3. Fill ""Last Name"" (optional) with 'Anne-Marie' (contains hyphen). 4. Fill ""Phone Number"" (mandatory) with '123456789'. 5. Fill ""Email"" (mandatory) with '<EMAIL>'. 6. Fill ""Date of Birth"" (mandatory) with '19-May-2007'. 7. Fill ""Website"" (optional) with 'www.domain.com'. 8. Submit the form. 9. Expect errors for ""First Name"" and ""Last Name"" as specified."
TC008,Edge Case: Website field invalid formats rejected; empty website accepted,"""","""",123456789,<EMAIL>,http://domain,Failure,Website: Enter a valid website. (eg: www.domain.com),Edge Case,"1. Navigate to the form URL. 2. Fill ""Phone Number"" (mandatory) with '123456789'. 3. Fill ""Email"" (mandatory) with '<EMAIL>'. 4. Fill ""Date of Birth"" (mandatory) with '19-May-2007'. 5. Fill ""Website"" (optional) with 'http://domain' (starts with protocol but missing www.). 6. Leave ""First Name"" and ""Last Name"" empty. 7. Submit the form. 8. Expect error: ""Website: Enter a valid website. (eg: www.domain.com)""."
TC009,"Edge Case: Date of Birth age boundaries tested for 20, 50, 75 years old accepted","""","""",123456789,<EMAIL>,www.domain.com,Success,"""",Edge Case,"1. Navigate to the form URL. 2. Fill ""Phone Number"" (mandatory) with '123456789'. 3. Fill ""Email"" (mandatory) with '<EMAIL>'. 4. Fill ""Date of Birth"" (mandatory) with '19-May-2005' (20 years old), submit and expect success. 5. Repeat with '19-May-1975' (50 years old), submit and expect success. 6. Repeat with '19-May-1950' (75 years old), submit and expect success. 7. Leave ""First Name"", ""Last Name"", and ""Website"" empty."
TC010,Negative Path: Multiple errors with all required fields empty and fields containing only spaces,"""","""","""","""","""",Failure,Phone Number: Enter a number for this field.|Email: Enter a valid email address. (eg: <EMAIL>)|Date of Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.|First Name: First name must contain alphabetic characters only.|Last Name: Last name must contain alphabetic characters only.,Negative Path,"1. Navigate to the form URL. 2. Fill ""First Name"" (optional) with '   ' (spaces). 3. Fill ""Last Name"" (optional) with '   ' (spaces). 4. Leave ""Phone Number"", ""Email"", and ""Date of Birth"" blank. 5. Leave ""Website"" empty. 6. Submit the form. 7. Expect errors for required fields and name fields due to spaces as specified."
