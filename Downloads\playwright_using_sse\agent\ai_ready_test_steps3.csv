Test_Case_ID,Test_Case_Description,First_Name,Last_Name,Date_of_Birth,Phone_Number,Email,Website,Expected_Result,Error_Message,Test_Case_Category,Test_Steps,Test_Steps_AI
TC021,Edge Case: First Name contains hyphen triggers error 'First name must contain alphabetic characters only.',<PERSON><PERSON><PERSON>,"""",12-May-2007,1234567890123,<EMAIL>,"""",Failure,First name must contain alphabetic characters only.,Edge Case,1. Navigate to https://emlabsform.onrender.com/ 2. First Name (optional): enter <PERSON><PERSON><PERSON> (contains hyphen) 3. Last Name (optional): leave empty 4. Date of Birth (mandatory): enter 12-May-2007 5. Phone Number (mandatory): enter 1234567890123 6. Email (mandatory): enter <EMAIL> 7. Website (optional): leave empty 8. Submit form 9. Expect failure with error 'First name must contain alphabetic characters only.',"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Page failed to load"".

2. Enter ""<PERSON><PERSON><PERSON>"" in field labeled ""First Name""; wait up to 5 seconds for field to be available; if field is not found, fail with message ""First Name field missing"".

3. Verify field labeled ""Last Name"" is present; leave empty; if field is not found, fail with message ""Last Name field missing"".

4. Enter ""12-May-2007"" in field labeled ""Date of Birth""; wait up to 5 seconds for field to be available; if field is not found, fail with message ""Date of Birth field missing"".

5. Enter ""1234567890123"" in field labeled ""Phone Number""; wait up to 5 seconds for field to be available; if field is not found, fail with message ""Phone Number field missing"".

6. Enter ""<EMAIL>"" in field labeled ""Email""; wait up to 5 seconds for field to be available; if field is not found, fail with message ""Email field missing"".

7. Verify field labeled ""Website"" is present; leave empty; if field is not found, fail with message ""Website field missing"".

8. Click button labeled ""Submit""; wait up to 10 seconds for form submission response; if button is not found, fail with message ""Submit button missing"".

9. Verify text ""First name must contain alphabetic characters only."" is visible; wait up to 10 seconds for error message to appear; if message is not found, fail with message ""Error message not displayed""."
TC022,Edge Case: Last Name contains hyphen triggers error 'Last name must contain alphabetic characters only',"""",Smith-Jones,12-May-2007,1234567890123,<EMAIL>,"""",Failure,Last name must contain alphabetic characters only,Edge Case,1. Navigate to https://emlabsform.onrender.com/ 2. First Name (optional): leave empty 3. Last Name (optional): enter Smith-Jones (contains hyphen) 4. Date of Birth (mandatory): enter 12-May-2007 5. Phone Number (mandatory): enter 1234567890123 6. Email (mandatory): enter <EMAIL> 7. Website (optional): leave empty 8. Submit form 9. Expect failure with error 'Last name must contain alphabetic characters only',"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Form page failed to load"".

2. Verify field labeled ""First Name"" is optional; wait up to 5 seconds for field to be available; if not found, fail with message ""First Name field missing"".

3. Enter ""Smith-Jones"" in field labeled ""Last Name""; wait up to 5 seconds for field to be available; if not found, fail with message ""Last Name field missing"".

4. Enter ""12-May-2007"" in field labeled ""Date of Birth""; wait up to 5 seconds for field to be available; if not found, fail with message ""Date of Birth field missing"".

5. Enter ""1234567890123"" in field labeled ""Phone Number""; wait up to 5 seconds for field to be available; if not found, fail with message ""Phone Number field missing"".

6. Enter ""<EMAIL>"" in field labeled ""Email""; wait up to 5 seconds for field to be available; if not found, fail with message ""Email field missing"".

7. Verify field labeled ""Website"" is optional; wait up to 5 seconds for field to be available; if not found, fail with message ""Website field missing"".

8. Click button labeled ""Submit""; wait up to 5 seconds for button to be clickable; if not found, fail with message ""Submit button missing"".

9. Verify text ""Last name must contain alphabetic characters only"" is visible; wait up to 10 seconds for message to appear; if not found, fail with message ""Error message not displayed""."
