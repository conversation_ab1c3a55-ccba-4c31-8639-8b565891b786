document.addEventListener('DOMContentLoaded', () => {
  const commandInput = document.getElementById('commandInput');
  const submitButton = document.getElementById('submitButton');
  const statusDiv = document.getElementById('status');
  const resultsdescDiv = document.getElementById('results');

  submitButton.addEventListener('click', async () => {
    const command = commandInput.value.trim().toLowerCase();

    statusDiv.textContent = 'Fetching current tab URL...';
    try {
      // Get the current tab's URL
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (!tabs || tabs.length === 0) {
        throw new Error('No active tab found');
      }
      const currentUrl = tabs[0].url;

      statusDiv.textContent = 'Processing Your query and calling API...';
      const response = await fetch('http://127.0.0.1:8001/generate-tests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: currentUrl, message: command }),
      });
      console.log('Raw response:', response);
      if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || response.statusText);
      }

      const data = await response.json();
      console.log('Parsed data:', data);
      statusDiv.textContent = `Status: ${data.status}`;

      // Build output HTML
      let output = '';

      // Display Test Cases
      if (data.test_cases && data.test_cases.length > 0) {
        output += '<h3>Test Cases</h3><ul>';
        data.test_cases.forEach(tc => {
          output += '<li>';
          output += `<strong>Test Case ID: ${tc.Test_Case_ID}</strong><br>`;
          output += `<strong>Description:</strong> ${tc.Test_Case_Description}<br>`;
          output += `<strong>Expected Result:</strong> ${tc.Expected_Result || 'N/A'}`;
          output += '</li>';
        });
        output += '</ul>';
      }

      // Display Detailed Test Steps
      if (data.detailed_test_steps && data.detailed_test_steps.length > 0) {
        output += '<h3>Test Cases</h3><ul>';
        data.detailed_test_steps.forEach(step => {
          output += '<li>';
          output += `<strong>Test Case ID: ${step.No}</strong><br>`;
          output += `<strong>Test Description:</strong> ${step['Test Description']}<br>`;
          if(step['Detailed Test Steps']){
          output += `<strong>Detailed Steps:</strong><pre><code>${step['Detailed Test Steps']}</code></pre>`;
          }
          output += '</li>';
        });
        output += '</ul>';
      }

      

      // Display Playwright Scripts
      if (data.typescript_scripts && data.typescript_scripts.length > 0) {
        // output += '<h3>Results</h3>';
        // data.typescript_scripts.forEach(script => {
        //   output += `<div><strong>Test Case ID: ${script.test_case_id}</strong><pre><code>${script.typescript_code}</code></pre></div>`;
        // });
        output += '<h3>Results</h3>';
        output += '<ul>';
        data.typescript_scripts.forEach(script => {
          output += '<li>';
          output += `<strong>Test Case ID:</strong> ${script.test_case_id}<br>`;
          output += `<strong>Test Description:</strong> ${script.test_description}<br>`;
          output += `<strong>Result:</strong><pre><code>${script.typescript_code}</code></pre>`;
          output += '</li>';
        });
        output += '</ul>';
      }

      // Display Processing Times
      if (data.processing_times) {
        output += '<h3>Processing Times</h3><ul>';
        for (const [key, value] of Object.entries(data.processing_times)) {
          const formattedKey = key
            .replace(/_/g, ' ')
            .replace(/\b\w/g, char => char.toUpperCase());
          output += `<li><strong>${formattedKey}:</strong> ${value.toFixed(2)} ${key === 'detailed_test_steps' || key === 'automation' ? 'minutes' : 'seconds'}</li>`;
        }
        output += '</ul>';
      }

      // Display Errors
      if (data.errors && data.errors.length > 0) {
        output += '<h3>Errors</h3><ul>';
        data.errors.forEach(error => {
          output += `<li>${error}</li>`;
        });
        output += '</ul>';
      }

      // If no data is returned, show a message
      if (!data.test_cases && !data.detailed_test_steps && !data.typescript_scripts && !data.errors && !data.processing_times) {
        output += '<p>No results returned from the API.</p>';
      }

      resultsdescDiv.innerHTML = output;
    } catch (error) {
  statusDiv.textContent = 'Error';
  if (resultsdescDiv) {
    if (error.message.includes('Failed to fetch')) {
      resultsdescDiv.innerHTML = '<p class="error">Could not connect to the Test Agent API. Please ensure the server is running on http://127.0.0.1:8000.</p>';
    } else {
      resultsdescDiv.innerHTML = '<p class="error">Error</p>';
    }
  } else {
    console.error('resultsdescDiv not found in DOM');
  }
}
  });
});