import { test, expect } from '@playwright/test';

test.describe('Form Input Validation Scenarios', () => {
  const URL = 'https://emlabsform.onrender.com/';
  const PAGE_LOAD_TIMEOUT = 10000;
  const FIELD_INPUT_TIMEOUT = 5000;
  const VALIDATION_TIMEOUT = 10000;

  test.beforeEach(async ({ page }) => {
    // 1. Navigate and verify page load
    const response = await page.goto(URL, {
      timeout: PAGE_LOAD_TIMEOUT,
      waitUntil: 'load',
    });
    await expect(response, 'Form page failed to load').not.toBeNull();
  });

  test('Mandatory and Optional Fields with Invalid Inputs Show Proper Errors', async ({ page }) => {
    // 2. First Name (optional)
    const firstName = page.getByLabel('First Name');
    await expect(firstName, 'First Name field missing').toBeVisible({ timeout: FIELD_INPUT_TIMEOUT });
    await firstName.fill('John123');

    // 3. Last Name (optional)
    const lastName = page.getByLabel('Last Name');
    await expect(lastName, 'Last Name field missing').toBeVisible({ timeout: FIELD_INPUT_TIMEOUT });
    await lastName.fill('Smith!');

    // 4. Phone Number (mandatory)
    const phone = page.getByLabel('Phone Number');
    await expect(phone, 'Phone Number field missing').toBeVisible({ timeout: FIELD_INPUT_TIMEOUT });
    await phone.fill('12345abc');

    // 5. Email (mandatory)
    const email = page.getByLabel('Email');
    await expect(email, 'Email field missing').toBeVisible({ timeout: FIELD_INPUT_TIMEOUT });
    await email.fill('user@@domain.com');

    // 6. Website (optional)
    const website = page.getByLabel('Website');
    await expect(website, 'Website field missing').toBeVisible({ timeout: FIELD_INPUT_TIMEOUT });
    await website.fill('http://domain.com');

    // 7. Date of Birth (mandatory)
    const dob = page.getByLabel('Date of Birth');
    await expect(dob, 'Date of Birth field missing').toBeVisible({ timeout: FIELD_INPUT_TIMEOUT });
    await dob.fill('19-May-2000');

    // 8. Click Submit
    const submitBtn = page.getByRole('button', { name: 'Submit' });
    await expect(submitBtn, 'Submit button missing').toBeVisible();
    await submitBtn.click();
    // Allow time for client-side validation to display messages
    await page.waitForTimeout(500);

    // 9. First Name error
    const fnError = page.getByText('First name must contain alphabetic characters only.');
    await expect(fnError, 'First Name error not displayed').toBeVisible({ timeout: VALIDATION_TIMEOUT });

    // 10. Last Name error
    const lnError = page.getByText('Last name must contain alphabetic characters only.');
    await expect(lnError, 'Last Name error not displayed').toBeVisible({ timeout: VALIDATION_TIMEOUT });

    // 11. Phone Number error
    const phoneError = page.getByText('Enter only numbers.');
    await expect(phoneError, 'Phone Number error not displayed').toBeVisible({ timeout: VALIDATION_TIMEOUT });

    // 12. Email error
    const emailError = page.getByText('Enter a valid email address. (eg: <EMAIL>)');
    await expect(emailError, 'Email error not displayed').toBeVisible({ timeout: VALIDATION_TIMEOUT });

    // 13. Website error
    const siteError = page.getByText('Enter a valid website. (eg: www.domain.com)');
    await expect(siteError, 'Website error not displayed').toBeVisible({ timeout: VALIDATION_TIMEOUT });
  });
});