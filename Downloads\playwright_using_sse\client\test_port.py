import socket
import subprocess
import urllib.request
import urllib.error
import time
import os
import sys
import psutil

def get_ports_starting_with(prefix):
    """
    Get a list of ports starting with the specified prefix.
    
    :param prefix: Prefix of the port number (e.g., '9')
    :return: List of ports currently in use that start with the prefix
    """
    in_use_ports = []
    
    try:
        # Use psutil to get all network connections
        connections = psutil.net_connections()
        
        # Collect unique ports that are listening and start with the prefix
        for conn in connections:
            if conn.status == psutil.CONN_LISTEN:
                port = conn.laddr.port
                # Convert port to string to check prefix
                if str(port).startswith(str(prefix)) and port not in in_use_ports:
                    in_use_ports.append(port)
        
        # Sort the ports for easier reading
        in_use_ports.sort()
        
        print(f"Ports currently in use starting with {prefix}:")
        for port in in_use_ports:
            print(f"Port {port}")
        
        return in_use_ports
    
    except Exception as e:
        print(f"Error finding used ports: {e}")
        return []

def get_process_by_port(port):
    """
    Find processes using the specified port.
    
    :param port: Port number to check
    :return: List of process information
    """
    found_processes = []
    
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                # Check if the process is listening on the port
                for conn in proc.connections():
                    if conn.status == psutil.CONN_LISTEN and conn.laddr.port == port:
                        found_processes.append({
                            'pid': proc.pid,
                            'name': proc.name(),
                            'cmdline': ' '.join(proc.cmdline()) if proc.cmdline() else ''
                        })
            except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                pass
    except Exception as e:
        print(f"Error finding processes: {e}")
    
    return found_processes

def wait_and_show_countdown(wait_time):
    """
    Wait for a specified time and show a countdown.
    
    :param wait_time: Time to wait in seconds
    """
    print(f"\nWaiting {wait_time} seconds before terminating processes...")
    for remaining in range(wait_time, 0, -1):
        sys.stdout.write(f"\rTime remaining: {remaining} seconds")
        sys.stdout.flush()
        time.sleep(1)
    print("\nProceeding with process termination...")
    
def close_port_processes(ports):
    """
    Close processes using the specified ports.
    
    :param ports: List of ports to close
    :return: Number of processes terminated
    """
    terminated_count = 0
     # Wait 60 seconds before terminating
    wait_and_show_countdown(60)
    # Iterate through specified ports
    for port in ports:
        processes = get_process_by_port(port)
        
        if not processes:
            print(f"No processes found using port {port}")
            continue
        
        print(f"\nProcesses using port {port}:")
        for proc in processes:
            print(f"PID: {proc['pid']}, Name: {proc['name']}, Command: {proc['cmdline']}")
        
        # Attempt to terminate processes
        for proc in processes:
            try:
                process = psutil.Process(proc['pid'])
                print(f"Terminating process PID {proc['pid']} ({proc['name']})")
                process.terminate()
                terminated_count += 1
            except (psutil.NoSuchProcess, psutil.AccessDenied) as e:
                print(f"Could not terminate process {proc['pid']}: {e}")
    
    return terminated_count

def find_available_port(start_port=9323, max_attempts=20):
    """
    Find the next available port.
    
    :param start_port: Starting port to check
    :param max_attempts: Maximum number of ports to try
    :return: First available port number
    """
    port = start_port
    attempts = 0
    
    try:
        while attempts < max_attempts:
            # Check if the port is in use
            connections = [conn for conn in psutil.net_connections() 
                           if conn.laddr.port == port and conn.status == psutil.CONN_LISTEN]
            
            if not connections:
                print(f"Port {port} is available")
                return port
            
            port += 1
            attempts += 1
        
        raise RuntimeError(f"Could not find an available port after {max_attempts} attempts")
    
    except Exception as e:
        print(f"Error finding available port: {e}")
        return None

def main():
    try:
        # Get list of ports starting with 9
        used_ports = get_ports_starting_with(9)
        
        if not used_ports:
            print("No ports starting with 9 are currently in use.")
            return
        
        # Close processes for used ports
        terminated_count = close_port_processes(used_ports)
        print(f"\nTerminated {terminated_count} process(es)")
        
        # Wait a moment for ports to be released
        time.sleep(2)
        
        # Find an available port
        available_port = find_available_port()
        if available_port:
            print(f"\nSelected Available Port: {available_port}")
    
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()