Test_Case_ID,Test_Case_Description,First_Name,Last_Name,Phone_Number,Email,Website,Expected_Result,Error_Message,Test_Case_Category,Test_Steps
TC001,"Happy Path: Mandatory fields only with valid inputs, optional fields empty",,,123456789,<EMAIL>,,Success,,Happy Path,"1. Navigate to https://emlabsform.onrender.com/. 2. Field 'First Name' is optional. Leave empty. 3. Field 'Last Name' is optional. Leave empty. 4. Field 'Phone Number' is mandatory. Enter ""123456789"". 5. Field 'Email' is mandatory. Enter ""<EMAIL>"". 6. Field 'Date of Birth' is mandatory. Enter ""27-May-2007"" (exactly 18 years before 27-May-2025). 7. Field 'Website' is optional. Leave empty. 8. Submit the form. 9. Verify form submission succeeds with no error messages."
TC002,Happy Path: All fields filled with valid inputs including optional fields,<PERSON><PERSON><PERSON>,<PERSON>,987654321,<EMAIL>,www.domain.com,Success,,Happy Path,"1. Navigate to https://emlabsform.onrender.com/. 2. Field 'First Name' is optional. Enter ""JohnDOE"". 3. Field 'Last Name' is optional. Enter ""Smith"". 4. Field 'Phone Number' is mandatory. Enter ""987654321"". 5. Field 'Email' is mandatory. Enter ""<EMAIL>"". 6. Field 'Date of Birth' is mandatory. Enter ""27-May-1980"". 7. Field 'Website' is optional. Enter ""www.domain.com"". 8. Submit the form. 9. Verify form submission succeeds with no error messages."
TC003,Negative Path: All required fields empty triggering required field errors,,,,,,Failure,Phone_Number: Enter a number for this field.|Email: Enter a valid email address. (eg: <EMAIL>)|Date_of_Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.,Negative Path,"1. Navigate to https://emlabsform.onrender.com/. 2. Field 'First Name' is optional. Leave empty. 3. Field 'Last Name' is optional. Leave empty. 4. Field 'Phone Number' is mandatory. Leave empty. 5. Field 'Email' is mandatory. Leave empty. 6. Field 'Date of Birth' is mandatory. Leave empty. 7. Field 'Website' is optional. Leave empty. 8. Submit the form. 9. Verify errors: Field 'Phone Number' should error with ""Enter a number for this field."". Field 'Email' should error with ""Enter a valid email address. (eg: <EMAIL>)"". Field 'Date of Birth' should error with ""Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.""."
TC004,Negative Path: Format validation errors combined for all applicable fields,John3,Doe!,123abc,user domain.com,http://domain.com,Failure,First_Name: First name must contain alphabetic characters only.|Last_Name: Last name must contain alphabetic characters only.|Phone_Number: Enter only numbers.|Email: Enter a valid email address. (eg: <EMAIL>)|Date_of_Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.|Website: Enter a valid website. (eg: www.domain.com),Negative Path,"1. Navigate to https://emlabsform.onrender.com/. 2. Field 'First Name' is optional. Enter ""John3"" (contains number). 3. Field 'Last Name' is optional. Enter ""Doe!"" (contains symbol). 4. Field 'Phone Number' is mandatory. Enter ""123abc"" (contains letters). 5. Field 'Email' is mandatory. Enter ""user domain.com"" (missing @, contains space). 6. Field 'Date of Birth' is mandatory. Enter ""28-May-2008"" (one day short of 18 years on 27-May-2025). 7. Field 'Website' is optional. Enter ""http://domain.com"" (starts with http:// without www.). 8. Submit the form. 9. Verify errors: Field 'First Name' should error with ""First name must contain alphabetic characters only."". Field 'Last Name' should error with ""Last name must contain alphabetic characters only."". Field 'Phone Number' should error with ""Enter only numbers."". Field 'Email' should error with ""Enter a valid email address. (eg: <EMAIL>)"". Field 'Date of Birth' should error with ""Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18."". Field 'Website' should error with ""Enter a valid website. (eg: www.domain.com)""."
TC005,Edge Case: Boundary and special character validations combined,José-Smith,Anne-Marie,123456789,<EMAIL>,www.domain,Failure,First_Name: First name must contain alphabetic characters only.|Last_Name: Last name must contain alphabetic characters only.|Date_of_Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.|Website: Enter a valid website. (eg: www.domain.com),Edge Case,"1. Navigate to https://emlabsform.onrender.com/. 2. Field 'First Name' is optional. Enter ""José-Smith"" (contains accented letter and hyphen). 3. Field 'Last Name' is optional. Enter ""Anne-Marie"" (contains hyphen). 4. Field 'Phone Number' is mandatory. Enter ""123456789"". 5. Field 'Email' is mandatory. Enter ""<EMAIL>"" (valid subdomain email). 6. Field 'Date of Birth' is mandatory. Enter ""28-May-2007"" (one day short of 18 years on 27-May-2025). 7. Field 'Website' is optional. Enter ""www.domain"" (missing domain extension). 8. Submit the form. 9. Verify errors: Field 'First Name' should error with ""First name must contain alphabetic characters only."". Field 'Last Name' should error with ""Last name must contain alphabetic characters only."". Field 'Date of Birth' should error with ""Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18."". Field 'Website' should error with ""Enter a valid website. (eg: www.domain.com)"". 10. Repeat submission with Date of Birth ""27-May-2007"" (exactly 18 years) and names without hyphens or accents to verify acceptance. 11. Test Date of Birth with ages 20, 50, 75 years by entering ""27-May-2005"", ""27-May-1975"", and ""27-May-1950"" respectively and verify acceptance."
