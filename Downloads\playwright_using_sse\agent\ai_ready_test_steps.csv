Test_Case_ID,Test_Case_Description,First_Name,Last_Name,Phone_Number,Email,Website,Expected_Result,Error_Message,Test_Case_Category,Test_Steps,Test_Steps_AI
TC001,"Happy Path: All mandatory fields correctly filled with valid data, optional fields empty","""","""",123456789,<EMAIL>,"""",Success,"""",Happy Path,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Leave empty. 3. Field 'Last Name' is optional. Leave empty. 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Leave empty. 7. Submit the form. 8. Verify form submission is successful.,"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Form page failed to load"".

2. Verify field labeled ""First Name"" is present; wait up to 5 seconds for field availability; if not found, fail with message ""First Name field missing""; leave field empty as it is optional.

3. Verify field labeled ""Last Name"" is present; wait up to 5 seconds for field availability; if not found, fail with message ""Last Name field missing""; leave field empty as it is optional.

4. Enter ""123456789"" in field labeled ""Phone Number""; wait up to 5 seconds for field availability; if not found, fail with message ""Phone Number field missing"".

5. Enter ""<EMAIL>"" in field labeled ""Email""; wait up to 5 seconds for field availability; if not found, fail with message ""Email field missing"".

6. Verify field labeled ""Website"" is present; wait up to 5 seconds for field availability; if not found, fail with message ""Website field missing""; leave field empty as it is optional.

7. Click button labeled ""Submit""; wait up to 5 seconds for button to be clickable; if not found, fail with message ""Submit button missing"".

8. Verify form submission is successful; wait up to 10 seconds for submission response; verify no text containing ""error"" is visible; if found, fail with message ""Form submission failed""."
TC002,"Happy Path: All fields filled with valid data including optional fields and edge cases (mixed case names, subdomain email, DOB exactly 18 years ago)",JohnDOE,Smith,987654321,<EMAIL>,www.domain.com,Success,"""",Happy Path,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Enter 'JohnDOE'. 3. Field 'Last Name' is optional. Enter 'Smith'. 4. Field 'Phone Number' is mandatory. Enter '987654321'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Enter 'www.domain.com'. 7. Field 'Date of Birth' is mandatory. Enter '19-May-2007' (exactly 18 years before 19-May-2025). 8. Submit the form. 9. Verify form submission is successful.,"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Page failed to load"".

2. Verify field ""First Name"" is optional; enter ""JohnDOE"" in field labeled ""First Name""; wait up to 5 seconds for field to be available; if field not found, fail with message ""First Name field missing"".

3. Verify field ""Last Name"" is optional; enter ""Smith"" in field labeled ""Last Name""; wait up to 5 seconds for field to be available; if field not found, fail with message ""Last Name field missing"".

4. Verify field ""Phone Number"" is mandatory; enter ""987654321"" in field labeled ""Phone Number""; wait up to 5 seconds for field to be available; if field not found, fail with message ""Phone Number field missing"".

5. Verify field ""Email"" is mandatory; enter ""<EMAIL>"" in field labeled ""Email""; wait up to 5 seconds for field to be available; if field not found, fail with message ""Email field missing"".

6. Verify field ""Website"" is optional; enter ""www.domain.com"" in field labeled ""Website""; wait up to 5 seconds for field to be available; if field not found, fail with message ""Website field missing"".

7. Verify field ""Date of Birth"" is mandatory; enter ""19-May-2007"" in field labeled ""Date of Birth""; wait up to 5 seconds for field to be available; if field not found, fail with message ""Date of Birth field missing"".

8. Click button ""Submit""; wait up to 5 seconds for button to be available; if button not found, fail with message ""Submit button missing"".

9. Verify form submission is successful; wait up to 10 seconds for submission confirmation; if submission fails, fail with message ""Form submission failed""."
TC003,Negative Path: Required fields empty triggers all required field error messages,"""","""","""","""","""",Failure,Phone_Number: Enter a number for this field.|Email: Enter a valid email address. (eg: <EMAIL>)|Date_of_Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.,Negative Path,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Leave empty. 3. Field 'Last Name' is optional. Leave empty. 4. Field 'Phone Number' is mandatory. Leave empty. 5. Field 'Email' is mandatory. Leave empty. 6. Field 'Website' is optional. Leave empty. 7. Field 'Date of Birth' is mandatory. Leave empty. 8. Submit the form. 9. Verify errors: Field 'Phone Number' should error with 'Enter a number for this field.' Field 'Email' should error with 'Enter a valid email address. (eg: <EMAIL>)' Field 'Date of Birth' should error with 'Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.',"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Page failed to load"".

2. Verify field labeled ""First Name"" is present and empty; if not found, fail with message ""First Name field missing"".

3. Verify field labeled ""Last Name"" is present and empty; if not found, fail with message ""Last Name field missing"".

4. Verify field labeled ""Phone Number"" is present and empty; if not found, fail with message ""Phone Number field missing"".

5. Verify field labeled ""Email"" is present and empty; if not found, fail with message ""Email field missing"".

6. Verify field labeled ""Website"" is present and empty; if not found, fail with message ""Website field missing"".

7. Verify field labeled ""Date of Birth"" is present and empty; if not found, fail with message ""Date of Birth field missing"".

8. Click button labeled ""Submit""; wait up to 10 seconds for form submission to complete.

9. Verify text ""Enter a number for this field."" is visible near field labeled ""Phone Number""; if not found, fail with message ""Phone Number error not displayed"".

10. Verify text ""Enter a valid email address. (eg: <EMAIL>)"" is visible near field labeled ""Email""; if not found, fail with message ""Email error not displayed"".

11. Verify text ""Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18."" is visible near field labeled ""Date of Birth""; if not found, fail with message ""Date of Birth error not displayed""."
TC004,Negative Path: Format validation errors for all fields with format restrictions,John123,Smith!,12345abc,user@@domain.com,http://domain.com,Failure,First_Name: First name must contain alphabetic characters only.|Last_Name: Last name must contain alphabetic characters only.|Phone_Number: Enter only numbers.|Email: Enter a valid email address. (eg: <EMAIL>)|Website: Enter a valid website. (eg: www.domain.com),Negative Path,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Enter 'John123'. 3. Field 'Last Name' is optional. Enter 'Smith!'. 4. Field 'Phone Number' is mandatory. Enter '12345abc'. 5. Field 'Email' is mandatory. Enter 'user@@domain.com'. 6. Field 'Website' is optional. Enter 'http://domain.com'. 7. Field 'Date of Birth' is mandatory. Enter '19-May-2000' (valid date to isolate format errors). 8. Submit the form. 9. Verify errors: Field 'First Name' should error with 'First name must contain alphabetic characters only.' Field 'Last Name' should error with 'Last name must contain alphabetic characters only.' Field 'Phone Number' should error with 'Enter only numbers.' Field 'Email' should error with 'Enter a valid email address. (eg: <EMAIL>)' Field 'Website' should error with 'Enter a valid website. (eg: www.domain.com)',"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Form page failed to load"".

2. Verify field ""First Name"" is optional; enter ""John123"" in field labeled ""First Name""; wait up to 5 seconds for field to accept input; if field not found, fail with message ""First Name field missing"".

3. Verify field ""Last Name"" is optional; enter ""Smith!"" in field labeled ""Last Name""; wait up to 5 seconds for field to accept input; if field not found, fail with message ""Last Name field missing"".

4. Verify field ""Phone Number"" is mandatory; enter ""12345abc"" in field labeled ""Phone Number""; wait up to 5 seconds for field to accept input; if field not found, fail with message ""Phone Number field missing"".

5. Verify field ""Email"" is mandatory; enter ""user@@domain.com"" in field labeled ""Email""; wait up to 5 seconds for field to accept input; if field not found, fail with message ""Email field missing"".

6. Verify field ""Website"" is optional; enter ""http://domain.com"" in field labeled ""Website""; wait up to 5 seconds for field to accept input; if field not found, fail with message ""Website field missing"".

7. Verify field ""Date of Birth"" is mandatory; enter ""19-May-2000"" in field labeled ""Date of Birth""; wait up to 5 seconds for field to accept input; if field not found, fail with message ""Date of Birth field missing"".

8. Click button ""Submit""; wait up to 10 seconds for form submission to complete; verify button is clicked; if button not found, fail with message ""Submit button missing"".

9. Verify text ""First name must contain alphabetic characters only."" is visible; wait up to 10 seconds for error message to appear; if text not found, fail with message ""First Name error not displayed"".

10. Verify text ""Last name must contain alphabetic characters only."" is visible; wait up to 10 seconds for error message to appear; if text not found, fail with message ""Last Name error not displayed"".

11. Verify text ""Enter only numbers."" is visible; wait up to 10 seconds for error message to appear; if text not found, fail with message ""Phone Number error not displayed"".

12. Verify text ""Enter a valid email address. (eg: <EMAIL>)"" is visible; wait up to 10 seconds for error message to appear; if text not found, fail with message ""Email error not displayed"".

13. Verify text ""Enter a valid website. (eg: www.domain.com)"" is visible; wait up to 10 seconds for error message to appear; if text not found, fail with message ""Website error not displayed""."
TC005,Negative Path: Date of Birth range validation errors (future date and under 18),"""","""",123456789,<EMAIL>,www.domain.com,Failure,Date_of_Birth: Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.,Negative Path,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Leave empty. 3. Field 'Last Name' is optional. Leave empty. 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Enter 'www.domain.com'. 7. Field 'Date of Birth' is mandatory. Enter '20-May-2007' (one day short of 18 years from 19-May-2025) and then try '20-May-2026' (future date). 8. Submit the form after each entry. 9. Verify errors: Field 'Date of Birth' should error with 'Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18.',"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Page failed to load"".

2. Verify field labeled ""First Name"" is present; if not found, fail with message ""First Name field missing""; leave field empty.

3. Verify field labeled ""Last Name"" is present; if not found, fail with message ""Last Name field missing""; leave field empty.

4. Verify field labeled ""Phone Number"" is present; if not found, fail with message ""Phone Number field missing""; enter ""123456789"" in field labeled ""Phone Number""; wait up to 5 seconds for field to accept input.

5. Verify field labeled ""Email"" is present; if not found, fail with message ""Email field missing""; enter ""<EMAIL>"" in field labeled ""Email""; wait up to 5 seconds for field to accept input.

6. Verify field labeled ""Website"" is present; if not found, fail with message ""Website field missing""; enter ""www.domain.com"" in field labeled ""Website""; wait up to 5 seconds for field to accept input.

7. Verify field labeled ""Date of Birth"" is present; if not found, fail with message ""Date of Birth field missing""; enter ""20-May-2007"" in field labeled ""Date of Birth""; wait up to 5 seconds for field to accept input.

8. Click button labeled ""Submit""; wait up to 10 seconds for form submission to complete.

9. Verify field labeled ""Date of Birth"" is present; if not found, fail with message ""Date of Birth field missing""; enter ""20-May-2026"" in field labeled ""Date of Birth""; wait up to 5 seconds for field to accept input.

10. Click button labeled ""Submit""; wait up to 10 seconds for form submission to complete.

11. Verify text ""Enter a valid date in dd-MMM-yyyy format and ensure you're at least 18."" is visible; if not found, fail with message ""Date of Birth error message not displayed""; wait up to 10 seconds for error message to appear."
TC006,"Edge Case: Boundary testing for names with invalid characters (hyphens, accented letters, spaces) and leading/trailing spaces",John-, José ,"""",<EMAIL>,"""",Failure,First_Name: First name must contain alphabetic characters only.|Last_Name: Last name must contain alphabetic characters only.,Edge Case,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Enter 'John-'. 3. Field 'Last Name' is optional. Enter ' José ' (with leading/trailing spaces and accented letter). 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Leave empty. 7. Field 'Date of Birth' is mandatory. Enter '19-May-2000' (valid date). 8. Submit the form. 9. Verify errors: Field 'First Name' should error with 'First name must contain alphabetic characters only.' Field 'Last Name' should error with 'Last name must contain alphabetic characters only.',"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Page failed to load"".

2. Verify field labeled ""First Name"" is optional; enter ""John-"" in field labeled ""First Name""; wait up to 5 seconds for field to accept input; if field is not found, fail with message ""First Name field missing"".

3. Verify field labeled ""Last Name"" is optional; enter "" José "" in field labeled ""Last Name""; wait up to 5 seconds for field to accept input; if field is not found, fail with message ""Last Name field missing"".

4. Verify field labeled ""Phone Number"" is mandatory; enter ""123456789"" in field labeled ""Phone Number""; wait up to 5 seconds for field to accept input; if field is not found, fail with message ""Phone Number field missing"".

5. Verify field labeled ""Email"" is mandatory; enter ""<EMAIL>"" in field labeled ""Email""; wait up to 5 seconds for field to accept input; if field is not found, fail with message ""Email field missing"".

6. Verify field labeled ""Website"" is optional; leave field labeled ""Website"" empty; if field is not found, fail with message ""Website field missing"".

7. Verify field labeled ""Date of Birth"" is mandatory; enter ""19-May-2000"" in field labeled ""Date of Birth""; wait up to 5 seconds for field to accept input; if field is not found, fail with message ""Date of Birth field missing"".

8. Click button labeled ""Submit""; wait up to 10 seconds for form submission to complete; if button is not found, fail with message ""Submit button missing"".

9. Verify text ""First name must contain alphabetic characters only."" is visible near field labeled ""First Name""; wait up to 10 seconds for error message to appear; if message is not found, fail with message ""First Name error not displayed"".

10. Verify text ""Last name must contain alphabetic characters only."" is visible near field labeled ""Last Name""; wait up to 10 seconds for error message to appear; if message is not found, fail with message ""Last Name error not displayed""."
TC007,"Edge Case: Website field invalid formats (missing www., missing domain extension)","""","""",123456789,<EMAIL>,www.domain,Failure,Website: Enter a valid website. (eg: www.domain.com),Edge Case,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Leave empty. 3. Field 'Last Name' is optional. Leave empty. 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter '<EMAIL>'. 6. Field 'Website' is optional. Enter 'www.domain' (missing domain extension). 7. Field 'Date of Birth' is mandatory. Enter '19-May-2000' (valid date). 8. Submit the form. 9. Verify error: Field 'Website' should error with 'Enter a valid website. (eg: www.domain.com)',"1. Navigate to page ""https://emlabsform.onrender.com/""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Page failed to load"".

2. Verify field labeled ""First Name"" is present and empty; wait up to 5 seconds for field availability; if not found, fail with message ""First Name field missing"".

3. Verify field labeled ""Last Name"" is present and empty; wait up to 5 seconds for field availability; if not found, fail with message ""Last Name field missing"".

4. Enter ""123456789"" in field labeled ""Phone Number""; wait up to 5 seconds for input to be accepted; if field is not found, fail with message ""Phone Number field missing"".

5. Enter ""<EMAIL>"" in field labeled ""Email""; wait up to 5 seconds for input to be accepted; if field is not found, fail with message ""Email field missing"".

6. Enter ""www.domain"" in field labeled ""Website""; wait up to 5 seconds for input to be accepted; if field is not found, fail with message ""Website field missing"".

7. Enter ""19-May-2000"" in field labeled ""Date of Birth""; wait up to 5 seconds for input to be accepted; if field is not found, fail with message ""Date of Birth field missing"".

8. Click button labeled ""Submit""; wait up to 10 seconds for form submission response; if button is not found, fail with message ""Submit button missing"".

9. Verify text ""Enter a valid website. (eg: www.domain.com)"" is visible near field labeled ""Website""; wait up to 10 seconds for error message to appear; if message is not found, fail with message ""Website error message not displayed""."
TC008,"Edge Case: Leading/trailing spaces trimmed in email and names, no error on valid trimmed input", John , Doe ,123456789, <EMAIL> ,www.domain.com,Success,"""",Edge Case,1. Navigate to https://emlabsform.onrender.com/ 2. Field 'First Name' is optional. Enter ' John ' (with leading/trailing spaces). 3. Field 'Last Name' is optional. Enter ' Doe ' (with leading/trailing spaces). 4. Field 'Phone Number' is mandatory. Enter '123456789'. 5. Field 'Email' is mandatory. Enter ' <EMAIL> ' (with leading/trailing spaces). 6. Field 'Website' is optional. Enter 'www.domain.com'. 7. Field 'Date of Birth' is mandatory. Enter '19-May-2000' (valid date). 8. Submit the form. 9. Verify form submission is successful and no errors are shown.,"1. Navigate to page ""https://emlabsform.onrender.com""; wait up to 10 seconds for page to load; verify page is loaded; if not loaded, fail with message ""Page failed to load"".

2. Verify field labeled ""First Name"" is optional; enter "" John "" in field labeled ""First Name""; wait up to 5 seconds for field to accept input; if field not found, fail with message ""First Name field missing"".

3. Verify field labeled ""Last Name"" is optional; enter "" Doe "" in field labeled ""Last Name""; wait up to 5 seconds for field to accept input; if field not found, fail with message ""Last Name field missing"".

4. Verify field labeled ""Phone Number"" is mandatory; enter ""123456789"" in field labeled ""Phone Number""; wait up to 5 seconds for field to accept input; if field not found, fail with message ""Phone Number field missing"".

5. Verify field labeled ""Email"" is mandatory; enter "" <EMAIL> "" in field labeled ""Email""; wait up to 5 seconds for field to accept input; if field not found, fail with message ""Email field missing"".

6. Verify field labeled ""Website"" is optional; enter ""www.domain.com"" in field labeled ""Website""; wait up to 5 seconds for field to accept input; if field not found, fail with message ""Website field missing"".

7. Verify field labeled ""Date of Birth"" is mandatory; enter ""19-May-2000"" in field labeled ""Date of Birth""; wait up to 5 seconds for field to accept input; if field not found, fail with message ""Date of Birth field missing"".

8. Click button labeled ""Submit""; wait up to 10 seconds for form submission to complete; if button not found, fail with message ""Submit button missing"".

9. Verify no text containing ""error"" is visible; wait up to 10 seconds for any error messages to appear; if error text is found, fail with message ""Error message detected after form submission""."
