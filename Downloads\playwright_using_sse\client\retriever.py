# retrieve_with_llm.py
from dotenv import load_dotenv
load_dotenv() # Load environment variables from .env file
import time
import chromadb
import os
import google.generativeai as genai
import sys # To read command line arguments

# --- Configuration ---
# Get the Google API Key from environment variables (now loaded by dotenv)
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
if not GOOGLE_API_KEY:
    print("Error: GOOGLE_API_KEY environment variable not set.")
    print("Please ensure you have a .env file in the same directory with GOOGLE_API_KEY='YOUR_API_KEY'")
    # We don't sys.exit() immediately here if imported, but the setup will fail.
    # The functions relying on genai will likely raise errors.
    # We add checks later.

# Configure the generative AI library
genai_configured = False
if GOOGLE_API_KEY:
    try:
        genai.configure(api_key=GOOGLE_API_KEY)
        genai_configured = True
    except Exception as e:
        print(f"Error configuring Google Generative AI: {e}")
        # genai_configured remains False


# Specify the SAME Gemini embedding model used for embedding documents
EMBEDDING_MODEL = "models/text-embedding-004"

# Specify the Gemini model for text generation (choose an appropriate model)
GENERATION_MODEL = "gemini-2.5-flash-preview-05-20" # Or "gemini-1.5-flash"

# Path for your ChromaDB database (must be the same as ingest.py)
DB_PATH = "./playwright_vector_db"

# Name of the ChromaDB collection (must be the same as ingest.py)
COLLECTION_NAME = "playwright_docs"

# Number of relevant documents to retrieve from ChromaDB
N_RESULTS = 5

# --- ChromaDB Setup ---
client = None
collection = None
chroma_ready = False

try:
    client = chromadb.PersistentClient(path=DB_PATH)
    collection = client.get_collection(name=COLLECTION_NAME)
    doc_count = collection.count()
    if doc_count == 0:
         print("Warning: The ChromaDB collection is empty. Please run 'ingest.py' first after scraping.")
         # We won't sys.exit() here, just warn. RAG function will return no results.
    else:
        chroma_ready = True
        # print(f"Successfully connected to collection '{COLLECTION_NAME}' with {doc_count} documents.") # Suppress for cleaner output

except Exception as e:
    print(f"Error connecting to ChromaDB or retrieving collection '{COLLECTION_NAME}': {e}")
    print("Please ensure 'ingest.py' was run successfully and the database directory exists.")
    # chroma_ready remains False

# --- LLM Setup ---
llm_model = None
if genai_configured:
    try:
        llm_model = genai.GenerativeModel(GENERATION_MODEL)
        # Optional: Check if the model is available - might add latency on import
        # list(genai.list_models())
    except Exception as e:
        print(f"Error initializing Generative Model '{GENERATION_MODEL}': {e}")
        llm_model = None # Ensure it's None if initialization failed


# --- Retrieval Function ---
def retrieve_documentation(query_text: str, n_results: int = 5) -> list[str] | None:
    """
    Embeds a query and searches the ChromaDB collection for similar documents.

    Args:
        query_text: The user's search query string.
        n_results: The number of results to return.

    Returns:
        A list of document texts (strings) if found,
        an empty list [] if no results found,
        or None if an error occurs or ChromaDB is not ready.
    """
    if not chroma_ready:
        print("ChromaDB is not ready. Cannot perform retrieval.")
        return None # Indicate error state

    if not query_text or not query_text.strip():
        print("Query text cannot be empty.")
        return [] # Treat empty query as no results

    if not genai_configured:
         print("Google Generative AI is not configured. Cannot generate embeddings.")
         return None # Indicate error state

    try:
        # Generate embedding for the query
        # Need to wrap this in try/except as well, as genai might fail here
        embedding_response = genai.embed_content(
            model=EMBEDDING_MODEL,
            content=query_text,
            task_type="RETRIEVAL_QUERY" # Use RETRIEVAL_QUERY for the query embedding
        )
        query_embedding = embedding_response['embedding']

        # Query ChromaDB
        results = collection.query(
            query_embeddings=[query_embedding],
            n_results=n_results,
            include=['documents'] # Only need the document text
        )

        # Check if results were returned and contain documents
        if results and results.get('documents') and results['documents'][0]:
             # results['documents'] is a list of lists (one inner list per query)
             # We only have one query, so access the first inner list
             return results['documents'][0]
        else:
             return [] # Return empty list if no documents found

    except Exception as e:
        print(f"An error occurred during retrieval: {e}", file=sys.stderr)
        return None # Indicate an error occurred


# --- LLM Generation Function ---
def generate_answer_from_docs(documents: list[str], user_query: str) -> str | None:
    """
    Uses an LLM to generate an answer based on retrieved documents and a query.

    Args:
        documents: A list of document text strings retrieved from the database.
        user_query: The original user query.

    Returns:
        The generated text response string, or None if generation fails,
        returns no text, or the LLM is not ready.
    """
    if not llm_model:
        print("LLM model is not ready. Cannot perform generation.")
        return None # Indicate error state

    if not documents:
        # This function expects documents, but defensive check is good
        print("No documents provided for LLM generation.")
        return None

    # Combine the retrieved document texts into a single context string
    context = "\n---\n".join(documents)

    # --- Construct the prompt for the LLM ---
    prompt = f"""The user will provide test steps or a feature document. Based on this input, you will:

                1. Analyze the user's goal and determine the relevant area of the Playwright documentation (TypeScript focus).
                2. Retrieve and present only documentation-based best practices from the official Microsoft Playwright documentation based on the provided Context.

                Your response must follow this format:
                - Start with the heading: "Documentation to achieve optimal automation script:"
                - List only documentation-based best practices in clear, concise text.
                - Always use TypeScript code snippets.
                - Do not generate full test scripts.
                - Act as a guide who provides advice to achieve optimal automation script.
                - Do not include any external links.
                - Do not add extra commentary or filler text before the heading.
                - Focus only on text-based locators, assertions, and validations based on the Context.
                - Emphasize official recommended approaches for interacting with elements, assertions, and validations found in the Context.
                - Dont give documentation for steps/messages not mentioned explicitly in the Context or Query.
                - Dont invent field, names or values other than the ones mentioned in the test step or implied by the Context.
                - Do not include any additional information or context not derived from the provided documents.
                - Do not include any information about the test step itself, only the *documentation* relevant to it.
                - Structure the output in the order of the test steps if specific test steps are provided in the query.
                - Structure the output in a holistic way if a feature specification is provided in the query.
                - Always aim for optimal documentation-based solutions found in the Context.
                - Strictly adhere to not provide validation documenation for steps not mentioned explicitly for text based validation in the Context.
                - Output complete code snippets relevant to the practices discussed.
                - Do not extend for extra validation documentation based on current steps.
                - The documenttaion will be used by an LLM agent to generate a test script based on the provided steps so it should be concrete.
                - Do not include documenation  outside the scope of the provided steps or feature specification.

                Special focus from Context:
                - For UI validation steps such as form validation, highlight best practices around locator strategies, assertion methods, and user interaction simulation *as described in the Context*.
                - Provide reasoning based solely on documented best practices found in the Context.

    Context:
    ---
    {context}
    ---

    Question: {user_query}

    Answer:
    """
    try:
        response = llm_model.generate_content(prompt)

        # Check if the response has text content
        if response and response.text:
            print(response.text)
            return response.text

        else:
            print("\nLLM did not return a text response.", file=sys.stderr)
            # You might inspect response.prompt_feedback or response.candidates for more info
            # print(response) # Uncomment for debugging if needed
            return None

    except Exception as e:
        print(f"\nAn error occurred during LLM generation: {e}", file=sys.stderr)
        # Check for specific errors like safety blocks
        if hasattr(e, '_result') and hasattr(e._result, 'prompt_feedback'):
                print(f"Prompt feedback: {e._result.prompt_feedback}", file=sys.stderr)
        if hasattr(e, '_result') and hasattr(e._result, 'candidates'):
                print(f"Candidates: {e._result.candidates}", file=sys.stderr)
        return None

# --- New Wrapper Function for RAG Process ---
def ask_playwright_docs(query_text: str, n_results: int = N_RESULTS) -> str | None:
    """
    Performs the complete RAG process: retrieves documents and generates an answer.

    Args:
        query_text: The user's search query or test step/feature description.
        n_results: The number of documents to retrieve.

    Returns:
        The generated answer string if successful,
        a specific message string if no relevant documents were found,
        or None if a retrieval or generation error occurred, or setup failed.
    """
    if not chroma_ready or not genai_configured or not llm_model:
        # Initial setup failed when imported
        print("RAG system is not fully initialized. Check previous error messages.")
        return None

    print(f"Processing query: \"{query_text}\"") # Add some logging

    # Step 1: Retrieve documents
    retrieved_docs = retrieve_documentation(query_text, n_results)

    # Handle retrieval outcomes
    if retrieved_docs is None:
        # Error occurred (message already printed by retrieve_documentation)
        return None # Indicate overall failure

    if not retrieved_docs:
        # No documents found
        print("No relevant documents found in the database for this query.")
        return "No relevant documentation found in the database for your query." # Return specific message

    print(f"Retrieved {len(retrieved_docs)} relevant document snippets.")

    # Step 2: Generate answer using LLM
    print("Generating answer using LLM...")
    generated_answer = generate_answer_from_docs(retrieved_docs, query_text)

    if generated_answer is None:
        # Generation failed (message already printed by generate_answer_from_docs)
        return None # Indicate overall failure

    # print("Answer generated successfully.") # Can add if you want verbose output
    return generated_answer # Return the successful answer string


# --- Main Execution (for command line usage) ---
if __name__ == "__main__":
    # This block ONLY runs when you execute retrieve_with_llm.py directly

    # Check if a command-line argument (the query) was provided
    if len(sys.argv) < 2:
        print("Usage: python retrieve_with_llm.py \"Your query about Playwright\"")
        sys.exit(1)

    # The user query is the first command-line argument
    user_query = sys.argv[1]

    # Call the main RAG function we created
    final_answer = ask_playwright_docs(user_query, N_RESULTS)

    # Handle the result from the RAG function
    if final_answer is None:
        # An error occurred during setup, retrieval, or generation (messages already printed)
        sys.exit(1) # Exit with error code

    elif final_answer.startswith("No relevant documentation found"):
        # Specific message for no documents
        print(final_answer)
        sys.exit(0) # Exit successfully, just no results

    else:
        # Successful generation
        print("\n--- Generated Answer ---")
        print(final_answer)
        print("------------------------")
        sys.exit(0) # Exit successfully