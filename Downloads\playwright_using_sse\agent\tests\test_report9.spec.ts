// tests/name-trim-validation.spec.ts
import { test, expect, Page } from '@playwright/test';

test.describe('First/Last Name Trim and Alphabetic Validation', () => {
  const FORM_URL = 'https://emlabsform.onrender.com/';
  const FN_ERROR = 'First name must contain alphabetic characters only.';
  const LN_ERROR = 'Last name must contain alphabetic characters only.';

  /**
   * Locate an input by its label text and assert it is visible.
   */
  async function getField(page: Page, label: string, missingMsg: string) {
    const field = page.getByLabel(label);
    await expect(field, missingMsg).toBeVisible({ timeout: 5000 });
    return field;
  }

  /**
   * Fill all non-name fields with valid data.
   */
  async function fillOtherFields(page: Page) {
    await (await getField(page, 'Phone Number', 'Phone Number field missing'))
      .fill('123456789', { timeout: 5000 });
    await (await getField(page, 'Email', 'Email field missing'))
      .fill('<EMAIL>', { timeout: 5000 });
    await (await getField(page, 'Website', 'Website field missing'))
      .fill('www.domain.com', { timeout: 5000 });
    await (await getField(page, 'Date of Birth', 'Date of Birth field missing'))
      .fill('14-May-2000', { timeout: 5000 });
  }

  /**
   * Click the Submit button and wait for the submission to complete.
   */
  async function submitForm(page: Page) {
    const btn = page.getByRole('button', { name: 'Submit' });
    await expect(btn, 'Submit button missing').toBeVisible({ timeout: 5000 });
    await btn.click();
    await page.waitForLoadState('networkidle', { timeout: 10000 });
  }

  test('should display trim/alphabetic errors for names with extra spaces', async ({ page }) => {
    // 1. Navigate to page
    await page.goto(FORM_URL, { waitUntil: 'load', timeout: 10000 })
      .catch(() => { throw new Error('Page failed to load'); });

    // 2. First Name with leading/trailing spaces
    const firstName = await getField(page, 'First Name', 'First Name field missing');
    await firstName.fill(' John ', { timeout: 5000 });

    // 3. Last Name with leading/trailing spaces
    const lastName = await getField(page, 'Last Name', 'Last Name field missing');
    await lastName.fill(' Smith ', { timeout: 5000 });

    // 4-7. Fill remaining fields with valid data
    await fillOtherFields(page);

    // 8. Submit
    await submitForm(page);

    // Ensure any errors are visible
    await page.evaluate(() => document.body.scrollIntoView());

    // 9. First Name validation error
    const fnError = page.getByText(FN_ERROR);
    await expect(fnError, 'First Name error not displayed')
      .toBeVisible({ timeout: 10000 });

    // 10. Last Name validation error
    const lnError = page.getByText(LN_ERROR);
    await expect(lnError, 'Last Name error not displayed')
      .toBeVisible({ timeout: 10000 });
  });
});