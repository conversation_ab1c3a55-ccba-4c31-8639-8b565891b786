import uuid
import psycopg2
import os
from dotenv import load_dotenv
load_dotenv() 
def insert_project(project_name, description=""):
    conn = psycopg2.connect(
        dbname="QATOOL",
        user="postgres",
        password= os.getenv("DB_PASSWORD"),
        host="localhost",
        port="5432"
    )
    cur = conn.cursor()

    project_id = str(uuid.uuid4())

    cur.execute("""
        INSERT INTO Projects (project_id, project_name, description)
        VALUES (%s, %s, %s)
    """, (project_id, project_name, description))

    conn.commit()
    cur.close()
    conn.close()
    print(f"✅ Inserted project '{project_name}' with ID {project_id}")

if __name__ == "__main__":
    insert_project("Form Testing", "Testing forms in web application")
