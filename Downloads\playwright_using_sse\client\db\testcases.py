import pandas as pd
import psycopg2
import uuid
from datetime import datetime
from dotenv import load_dotenv
import os
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# Load environment variables
load_dotenv()

# DB connection settings
DB = {
    "dbname": "QATOOL",
    "user": "postgres",
    "password": os.getenv("DB_PASSWORD"),
    "host": "localhost",
    "port": "5432"
}

def get_project_and_link_ids(project_name, link_url):
    logging.info(f"Fetching project_id for project: '{project_name}'")
    with psycopg2.connect(**DB) as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT project_id FROM Projects WHERE project_name = %s", (project_name,))
            project = cur.fetchone()
            if not project:
                raise ValueError(f"❌ Project '{project_name}' not found.")
            project_id = project[0]
            logging.info(f"✅ Found project_id: {project_id}")

            logging.info(f"Fetching link_id for URL: '{link_url}'")
            cur.execute("SELECT link_id FROM ProjectLinks WHERE url = %s", (link_url,))
            link = cur.fetchone()
            if not link:
                raise ValueError(f"❌ Link '{link_url}' not found.")
            link_id = link[0]
            logging.info(f"✅ Found link_id: {link_id}")

            return project_id, link_id

def insert_testcases_from_dataframe(df, project_name, link_url):
    logging.info("⏳ Starting test case insertion...")
    project_id, link_id = get_project_and_link_ids(project_name, link_url)

    with psycopg2.connect(**DB) as conn:
        with conn.cursor() as cur:
            for idx, row in df.iterrows():
                testcase_id = str(uuid.uuid4())
                name = row.get("Test_Case_Description")
                description = row.get("Test_Steps")
                created_at = datetime.utcnow()

                if not name:
                    logging.warning(f"⚠️ Row {idx} missing Test_Case_Description. Skipping.")
                    continue

                logging.info(f"📌 Inserting test case: {name}")
                cur.execute("""
                    INSERT INTO TestCases (testcase_id, project_id, link_id, name, description, created_at)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    testcase_id,
                    project_id,
                    link_id,
                    name,
                    description,
                    created_at
                ))

        logging.info(f"✅ Successfully inserted {len(df)} test cases.")


def process_test_case(testcase):
    """
    Dummy function to process a single test case.
    Replace with your actual logic.
    """
    logging.info(f"Processing test case: {testcase['name']}")
    print(f"TestCase ID: {testcase['testcase_id']}")
    print(f"Name: {testcase['name']}")
    print(f"Description: {testcase['description']}")
    # Your processing logic here

def fetch_testcases(project_name, url):
    """
    Returns a list of test case rows for a given project name and URL.
    Each row is a dict: {testcase_id, name, description}
    """
    with psycopg2.connect(**DB) as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT project_id FROM Projects WHERE project_name = %s", (project_name,))
            project = cur.fetchone()
            if not project:
                raise ValueError(f"Project '{project_name}' not found.")
            project_id = project[0]

            cur.execute("""
                SELECT link_id FROM ProjectLinks
                WHERE url = %s AND project_id = %s
            """, (url, project_id))
            link = cur.fetchone()
            if not link:
                raise ValueError(f"URL '{url}' not found for project '{project_name}'.")
            link_id = link[0]

            cur.execute("""
                SELECT testcase_id, name, description
                FROM TestCases
                WHERE project_id = %s AND link_id = %s
                ORDER BY created_at
            """, (project_id, link_id))

            return [
                {
                    "testcase_id": row[0],
                    "name": row[1],
                    "description": row[2]
                }
                for row in cur.fetchall()
            ]