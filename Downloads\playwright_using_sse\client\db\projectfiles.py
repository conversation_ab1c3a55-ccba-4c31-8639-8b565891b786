import uuid
import psycopg2
import os 
from dotenv import load_dotenv
load_dotenv() 
DB = {
    "dbname": "QATOOL",
    "user": "postgres",
    "password": os.getenv("DB_PASSWORD"),  
    "host": "localhost",
    "port": "5432"
}

def get_project_id_by_name(project_name):
    conn = psycopg2.connect(**DB)
    cur = conn.cursor()

    cur.execute("SELECT project_id FROM Projects WHERE project_name = %s", (project_name,))
    result = cur.fetchone()

    cur.close()
    conn.close()

    if result:
        return result[0]  # UUID
    else:
        raise ValueError(f"❌ Project '{project_name}' not found.")

def insert_project_file(project_name, filename, s3_link):
    project_id = get_project_id_by_name(project_name)
    file_id = str(uuid.uuid4())

    conn = psycopg2.connect(**DB)
    cur = conn.cursor()

    cur.execute("""
        INSERT INTO ProjectFiles (file_id, project_id, filename, s3_link)
        VALUES (%s, %s, %s, %s)
    """, (file_id, project_id, filename, s3_link))

    conn.commit()
    cur.close()
    conn.close()

    print(f"✅ Inserted file '{filename}' for project '{project_name}'")

import psycopg2
import os
from dotenv import load_dotenv

# Load .env variables (make sure DB_PASSWORD is set in your .env file)
load_dotenv()

DB = {
    "dbname": "QATOOL",
    "user": "postgres",
    "password": os.getenv("DB_PASSWORD"),
    "host": "localhost",
    "port": "5432"
}

def get_project_file_url(project_name: str, filename: str) -> str:
    """
    Returns the URL associated with a given project and file.
    
    Args:
        project_name (str): The name of the project.
        filename (str): The filename to match.

    Returns:
        str: The associated URL or None if not found.
    """
    query = """
        SELECT l.url
        FROM ProjectLinks l
        JOIN Projects p ON l.project_id = p.project_id
        JOIN ProjectFiles f ON l.file_id = f.file_id
        WHERE p.project_name = %s AND f.filename = %s
    """
    try:
        with psycopg2.connect(**DB) as conn:
            with conn.cursor() as cur:
                cur.execute(query, (project_name, filename))
                result = cur.fetchone()
                return result[0] if result else None
    except Exception as e:
        print(f"❌ Error fetching URL: {e}")
        return None

if __name__ == "__main__":
    insert_project_file(
        "Form Testing",
        "form_spec_google_doc",
        "https://docs.google.com/document/d/1Vb9M3a8ehelH2q2DqqjNlU0aO_GtZ7We4sUZzVBN94E/edit?tab=t.0"
    )
