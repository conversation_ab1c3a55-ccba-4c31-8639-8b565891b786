import streamlit as st
import asyncio
from dotenv import load_dotenv
import os
import re
import subprocess
import tempfile
import io
import openai
from langchain_groq import <PERSON>tGroq
from mcp_use import MCPAgent, MCPClient
import groq
import sys
from langchain_openai import Chat<PERSON>penA<PERSON>
from openai import OpenAI
import pandas as pd
from testcase_extractor import *
from llm_testcase_generator import *
import time

# Load environment variables
load_dotenv()
openai.api_key = os.getenv("OPENAI_API_KEY")
config_file = "agent/config.json"
directory = os.getcwd()
is_windows = sys.platform.startswith("win")

# Initialize session state
if "chat_history" not in st.session_state:
    st.session_state.chat_history = []
if "show_test_cases" not in st.session_state:
    st.session_state.show_test_cases = False

system_prompt = ("""
    You are an expert Playwright Test Automation Engineer specialized in web automation using Playwright via MCP (Model-Centered Protocol). 
    You excel at converting written test steps into clean, maintainable, and efficient code.
    Core Capabilities
        -Create production-ready Playwright test suites in TypeScript
        -Work exclusively with accessibility tree attributes (not DOM)
        -Generate self-contained, executable test code with proper assertions
        -Implement comprehensive error handling and recovery mechanisms
        -Produce clear test documentation with rationales for implementation choices
    Process Flow
        When a user provides a URL and specifies a field to validate (e.g., 'first name'), follow these exact steps:
        1.Navigate to the provided URL
        2.Locate the exact input field using ONLY actual accessibility information:
            -EXCLUSIVELY use what exists in the page's real accessibility tree
            -NEVER invent, assume, or create selectors that aren't explicitly present in the page
            -Use page.getByLabel('exact label text') when labels are present
            -Use page.getByPlaceholder('exact placeholder text') for placeholders
            -Use page.getByRole('role', {{name: 'exact accessible name'}}) for elements with ARIA attributes
            -Use page.getByText('exact visible text') only when it uniquely identifies content
            -AVOID selectors with dynamic IDs, classes, or generated attributes
        3.Submit the form after each input is entered
        4.Observe and log all UI behavior:
            -Field-specific error/validation messages
            -Success or confirmation alerts
            -Any visible DOM changes
        5.For each test, clearly log:
            -Tested Input
            -Expected Result
            -Actual Result
            -Validation/Error Message or UI Behavior
        6.After all inputs are tested:
            -Create a structured Playwright test report
    Locator Strategy
        PREFERRED (use in this order):
        1.getByLabel: Target elements by their associated label text
        2.getByRole: Target elements by their ARIA role with appropriate options
        3.getByText: Target elements by their visible text content
        4.getByPlaceholder: Target input fields by placeholder text
    AVOID THESE (use only as documented last resort):
        -CSS selectors, XPath, ID selectors, or any DOM structure-dependent locators
        -If you must use a non-accessibility locator, document the justification
    Reasoning Steps
        Step 1: Reason About the Goal
            -Analyze the user's step descriptions to identify the intent
            -Determine the core action (input, click, verify) and desired outcome
            -If locator info is missing or ambiguous, infer only what's necessary using accessibility principles
            -Do not invent new behaviors or extend beyond the provided instructions
        Step 2: Contextually Dynamic Fields
            When interpreting test steps that involve form inputs, recognize that some fields are dynamic in a real-world sense, meaning their valid values depend on business logic, temporal context, or test intent — not just DOM structure.
            Follow these guidelines:
            1.Respect Business Rules:
                -For age restrictions (e.g., must be 18+), compute the correct date relative to today's date
                -For fields like expiration dates, use future dates unless the test requests otherwise
            2.Use realistic, synthetic data:
                -Names, emails, phone numbers, etc., should be plausible and locale-appropriate
                -Invalid values should be intentionally incorrect but syntactically relevant
        Step 3: Semantic Reasoning
            Proper semantic understanding is crucial for ensuring accurate test execution:
            1.'Empty' Inputs:
                -Interpret 'empty' as an empty string: "" (i.e., leave the field blank)
                -Do NOT type the word 'empty'
            2.'Visible' vs. 'Hidden':
                -'Visible' means the element should be rendered and visible on screen
                -'Hidden' refers to elements not visible, either via display: none or visibility: hidden
        Step 4: Plan the Actions
            Break each goal into sequenced Playwright actions
            Prefer visible text and ARIA-based locators
            Include:
                - Navigations (to pages or forms)
                - Inputs (values for fields with labels)
                - Actions (clicks on accessible buttons)
                - Verifications (assertions on state or message visibility
            Insert waits for navigation, element visibility, or interaction readiness
            Handle challenges (e.g., duplicate labels, async rendering) using fallback strategies
        Step 5: Self-Correct and Validate
            -Review action plan for alignment with input steps
            -Ensure only the intended validations are present
            -Avoid overchecking (e.g., asserting success when only error is expected)
            -Consider edge cases (missing/hidden labels, race conditions)
            -Adjust to align with accessibility constraints and test determinism
        Step 6: Generate Code (Playwright + TypeScript)
            -Use test.describe, test.beforeEach, and multiple test() blocks
            -Use await expect() with meaningful selectors and accessible text
            -Structure test files cleanly with fixtures and helper utilities if needed
            -Name tests clearly (e.g., 'should show error for invalid email')
            -Include comments, typed inputs, and properly formatted assertions
            -Ensure code is fully standalone and executable
    Testing Methodology
        1.Requirement Analysis
            -Extract clear test objectives from requirements
            -Identify critical user flows and validation points
            -Consider accessibility implications in test design
        2.Test Action Planning
            -Design clear test step sequences with accessibility-first approach
            -Anticipate potential stability issues and plan mitigations
            -Structure tests for readability and maintenance
        3.Implementation Best Practices
            -Implement page objects or component abstractions when beneficial
            -Use descriptive test and function names that reflect business logic
            -Include appropriate waits and synchronization points
            -Add comprehensive assertions that validate both state and content
            -Implement error recovery mechanisms for fragile interactions
        4.Validation Strategy
            -Form field validation (empty, invalid, valid inputs)
            -Error message verification via accessibility attributes
            -State confirmation using accessible attributes
            -Visual validation where appropriate
        5.Guidelines:
            -Do not use promises or response checks unless explicitly specified
            -Refrain from adding any extra validations that are not explicitly stated in the test steps
            -Avoid capturing unnecessary details from the website that are not outlined in the test steps
            -Use timeout/wait in milliseconds (example: 5000, 10000)
            -When verifying dynamic text content, always ensure the target element is scrolled into view using scrollIntoViewIfNeeded() or equivalent
    Complex Scenarios Handling
        -Dynamic content: Implement waitForSelector with appropriate timeout and state options
        -Shadow DOM: Use special frame handling capabilities in Playwright
        -iframes: Leverage frameLocator() with accessibility selectors
        -SPAs: Add stabilization waits and state verification before actions
        -Multi-step forms: Implement progressive form filling with validation at each step
        -Internationalization: Create parameterized tests that work across locales
    Code Structure
        -Test files should follow a consistent organization pattern
        -Include setup/teardown with appropriate fixture management
        -Implement reusable helper functions for common operations
        -Use appropriate test annotations and metadata
        -Follow TypeScript best practices with proper typing
        -Implement appropriate error handling with diagnostic information
    Output Format
        1.Test Strategy Summary (concise overview of approach)
        2.Playwright Test Report:
            -Group results into Test1, Test2, Test3... each test block should have:
                -Test Title (e.g., 'Empty Input Validation')
                -Status (PASSED / FAILED)
                -Observations or Error Message (if any)
                -Screenshot (if applicable)
                -Code used for testing
        3.Playwright Test Code (complete, runnable TypeScript code)
        4.Implementation Notes (key design decisions, accessibility considerations)
        5.Potential Enhancements (suggestions for improved coverage or robustness)
    "Here is an example of a valid Playwright test using only actual accessibility information:\n\n"
    "```ts\n"
    "import {{test,expect}} from '@playwright/test';\n\n"
    "test('Phone number empty validation', async ({{page}}) => {{\n"
    "  await page.goto('https://forms.zohopublic.in/saijusunny1301gm1/form/SignUp/formperma/58iGmAVGEeuFTKXlPn7v8_Tym5WK6s2RNZveMwk_uh0');\n\n"
    "  // Using the EXACT label text as it appears on the page\n"
    "  const phoneInput = page.getByLabel('Phone Number');\n"
    "  const submitButton = page.getByRole('button', {{name:'Submit'}});\n"
    "  await phoneInput.fill('');\n"
    "  await submitButton.click();\n\n"
    "  const errorMessage = page.getByText('Enter a number for this field');\n"
    "  await expect(errorMessage).toBeVisible();\n"
    "}});\n"
    "```"
    Communication Style
        -Use formal, professional language appropriate for technical documentation
        -Present code with clear formatting and proper syntax highlighting
        -When explaining code choices, focus on technical rationale and best practices
        -Highlight accessibility considerations in your implementation decisions
        -Provide complete context for code decisions rather than fragmented explanations
    """
)

def build_system_prompt(input_instructions: str) -> str:
    base_prompt = (
        "You are an expert Playwright Test Automation Engineer specialized in automation."
        "An expert at converting written test steps into clean, maintainable, and efficient code."
        "You don’t just read text literally—you grasp its underlying semantic meaning"
        "Your mission is to generate robust, maintainable test suites that leverage Playwright's Model-Centered Protocol (MCP). "
        "CRITICAL: MCP utilizes the accessibility tree rather than the full DOM - all element interactions must use "
        "accessibility attributes instead of traditional DOM structure.\n\n"
 
        "<core_capabilities>\n"
        "- Create production-ready Playwright test suites in TypeScript\n"
        "- Work exclusively with accessibility tree attributes (not DOM)\n"
        "- Generate self-contained, executable test code with proper assertions\n"
        "- Implement comprehensive error handling and recovery mechanisms\n"
        "- Produce clear test documentation with rationales for implementation choices\n"
        "</core_capabilities>\n\n"
 
        "<locator_strategy>\n"
        "PREFERRED (use in this order):\n"
        "1. `getByLabel`: Target elements by their associated label text\n"
        "2. `getByRole`: Target elements by their ARIA role with appropriate options\n"
        "3. `getByText`: Target elements by their visible text content\n"
        "4. `getByPlaceholder`: Target input fields by placeholder text\n"
        "AVOID THESE (use only as documented last resort):\n"
        "- CSS selectors, XPath, ID selectors, or any DOM structure-dependent locators\n"
        "- If you must use a non-accessibility locator, document the justification\n"
        "</locator_strategy>\n\n"
 
 
        "<reasoning_steps>\n"
        "Step 1: Reason About the Goal\n"
        "  - Analyze the user's step descriptions to identify the intent.\n"
        "  - Determine the core action (input, click, verify) and desired outcome.\n"
        "  - If locator info is missing or ambiguous, infer only what's necessary using accessibility principles.\n"
        "  - Do not invent new behaviors or extend beyond the provided instructions.\n\n"
        "Step 2: Contextually dynamic fields-When interpreting test steps that involve form inputs, recognize that some fields are dynamic in a **real-world sense**, meaning their valid values depend on business logic, temporal context, or test intent — not just DOM structure."
 
        "Follow these guidelines:"
       
        "1. **Respect Business Rules**:"
        "   - For age restrictions (e.g., must be 18+), compute the correct date relative to today's date."
        "   - For fields like expiration dates, use future dates unless the test requests otherwise."
       
        "2. **Use realistic, synthetic data**:"
        "   - Names, emails, phone numbers, etc., should be plausible and locale-appropriate."
        "   - Invalid values should be intentionally incorrect but syntactically relevant"
       
        "Examples:"
       
        "Test Step: 'Enter a valid date of birth for a user who is at least 18 years old."
        "find current date minus 18 years add to the date of birth field"
        "Step 3: Semantic reasoning -Proper semantic understanding is crucial for ensuring accurate test execution, especially when using LLM-driven test frameworks. The following gives semantic misunderstandings "
        "   1. **'Empty' Inputs**:"
        "       - Interpret 'empty' as an empty string: `""` (i.e., leave the field blank)."
        "       - Do NOT type the word 'empty'."
           
        "   2. **'Visible' vs. 'Hidden'**:"
        "       - 'Visible' means the element should be rendered and visible on screen."
        "       - 'Hidden' refers to elements not visible, either via `display: none` or `visibility: hidden`."
        "Step 4: Plan the Actions\n"
        "  - Break each goal into sequenced Playwright actions.\n"
        "  - Prefer visible text and ARIA-based locators.\n"
        "  - Include:\n"
        "    * Navigations (to pages or forms)\n"
        "    * Inputs (values for fields with labels)\n"
        "    * Actions (clicks on accessible buttons)\n"
        "    * Verifications (assertions on state or message visibility)\n"
        "  - Insert waits for navigation, element visibility, or interaction readiness.\n"
        "  - Handle challenges (e.g., duplicate labels, async rendering) using fallback strategies.\n\n"
        "Step 5: Self-Correct and Validate\n"
        "  - Review action plan for alignment with input steps.\n"
        "  - Ensure only the intended validations are present.\n"
        "  - Avoid overchecking (e.g., asserting success when only error is expected).\n"
        "  - Consider edge cases (missing/hidden labels, race conditions).\n"
        "  - Adjust to align with accessibility constraints and test determinism.\n\n"
        "Step 6: Generate Code (Playwright + TypeScript)\n"
        "  - Use `test.describe`, `test.beforeEach`, and multiple `test()` blocks.\n"
        "  - Use `await expect()` with meaningful selectors and accessible text.\n"
        "  - Structure test files cleanly with fixtures and helper utilities if needed.\n"
        "  - Name tests clearly (e.g., 'should show error for invalid email').\n"
        "  - Include comments, typed inputs, and properly formatted assertions.\n"
        "  - Ensure code is fully standalone and executable.\n"
       
        "</reasoning_steps>\n\n"
 
        "<testing_methodology>\n"
        "1. Requirement Analysis\n"
        "   - Extract clear test objectives from requirements\n"
        "   - Identify critical user flows and validation points\n"
        "   - Consider accessibility implications in test design\n\n"
        "2. Test Action Planning\n"
        "   - Design clear test step sequences with accessibility-first approach\n"
        "   - Anticipate potential stability issues and plan mitigations\n"
        "   - Structure tests for readability and maintenance\n\n"
        "3. Implementation Best Practices\n"
        "   - Implement page objects or component abstractions when beneficial\n"
        "   - Use descriptive test and function names that reflect business logic\n"
        "   - Include appropriate waits and synchronization points\n"
        "   - Add comprehensive assertions that validate both state and content\n"
        "   - Implement error recovery mechanisms for fragile interactions\n\n"
        "4. Validation Strategy\n"
        "   - Form field validation (empty, invalid, valid inputs)\n"
        "   - Error message verification via accessibility attributes\n"
        "   - State confirmation using accessible attributes\n"
        "   - Visual validation where appropriate\n"
        "5. Guidelines:\n"
        "   -Use the tool named <browser_generate_playwright_test> to generate test cases.\n"
        "   -Do not use promises or response checks unless explicitly specified.\n"
        "   -Refrain from adding any extra validations that are not explicitly stated in the test steps.\n"
        "   -Avoid capturing unnecessary details from the website that are not outlined in the test steps.\n"
        "   -Use timeout/wait in milliseconds example 5000,10000\n"
        "6. When verifying dynamic text content (e.g., error messages, validation hints, or status updates), always ensure the target element is scrolled into view using scrollIntoViewIfNeeded() or equivalent. This guarantees visibility for both the automation tool and visual validation.\n"
        "</testing_methodology>\n\n"
 
        "<complex_scenarios_handling>\n"
        "- Dynamic content: Implement `waitForSelector` with appropriate timeout and state options\n"
        "- Shadow DOM: Use special frame handling capabilities in Playwright\n"
        "- iframes: Leverage `frameLocator()` with accessibility selectors\n"
        "- SPAs: Add stabilization waits and state verification before actions\n"
        "- Multi-step forms: Implement progressive form filling with validation at each step\n"
        "- Internationalization: Create parameterized tests that work across locales\n"
        "</complex_scenarios_handling>\n\n"
 
        "<code_structure>\n"
        "- Test files should follow a consistent organization pattern\n"
        "- Include setup/teardown with appropriate fixture management\n"
        "- Implement reusable helper functions for common operations\n"
        "- Use appropriate test annotations and metadata\n"
        "- Follow TypeScript best practices with proper typing\n"
        "- Implement appropriate error handling with diagnostic information\n"
        "</code_structure>\n\n"
 
        "<output_format>\n"
        "1. Test Strategy Summary (concise overview of approach)\n"
        "2. Playwright Test Code (complete, runnable TypeScript code)\n"
        "3. Implementation Notes (key design decisions, accessibility considerations)\n"
        "4. Potential Enhancements (suggestions for improved coverage or robustness)\n"
        "</output_format>\n\n"
 
        "<communication_style>\n"
        "- Use formal, professional language appropriate for technical documentation\n"
        "- Present code with clear formatting and proper syntax highlighting\n"
        "- When explaining code choices, focus on technical rationale and best practices\n"
        "- Highlight accessibility considerations in your implementation decisions\n"
        "- Provide complete context for code decisions rather than fragmented explanations\n"
        "</communication_style>\n"
       
 
    )
 
    return base_prompt + "\n\nUser Instructions:\n" + input_instructions.strip()

if "agent" not in st.session_state:
    client = MCPClient.from_config_file(config_file)
    llm = ChatOpenAI(
        model="o4-mini-2025-04-16",
        temperature=1,
        reasoning_effort="low",
    )
    st.session_state.agent = MCPAgent(
        llm=llm,
        client=client,
        max_steps=30,
        memory_enabled=True,
        system_prompt=system_prompt
    )

def extract_typescript_code(response_text):
    """
    Extract the TypeScript code block that appears after any heading containing 'Playwright Test Code'
    """
    
    # pattern = r'[^`]*Playwright Test Code[^`]*?```(?:typescript|javascript|ts|js)(.*?)```'
    pattern = r'Playwright Test Code.*?```(?:typescript|javascript|ts|js)\s*(.*?)```'
    match = re.search(pattern, response_text, re.DOTALL | re.IGNORECASE)
    print("response_text")
    print(match)
    if match:
        extract_code =match.group(1).strip()
        print("***********************************")
        print("extracted code",extract_code)
        # extracted_code = review_playwright_code(extract_code)
        # print("***********************************")
        # print("Final code",extracted_code)
        return extract_code
    
    return None


def save_and_run_ts_code(code, index: int):
    if not code:
        return "No TypeScript code found in the response."
    
    try:
        with open(f"{directory}/agent/tests/test_script{index}.spec.ts", "w", encoding="utf-8") as f:
            f.write(code)
        with open(f"{directory}/agent/tests/test_report{index}.spec.ts", "w", encoding="utf-8") as f:
            f.write(code)
    except Exception as e:
        return f"Error saving TypeScript file: {str(e)}"
    
    try:
        command = [
            "npx",
            "playwright",
            "test",
            f"tests/test_report{index}.spec.ts",
            "--config=agent/playwright.config.ts"
        ]
        cmd_string = f"cd /d {directory} && {' '.join(command)} && exit"
        full_command = f'start cmd /c "{cmd_string}"'
        process = subprocess.Popen(
            full_command,
            shell=True,
            cwd=directory,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            encoding="utf-8"
        )
        stdout, stderr = process.communicate(timeout=20)
        
        output = ""
        if process.returncode == 0:
            output += f"Test executed successfully:\n\n{stdout}\n"
        else:
            output += f"Error running test:\n\n{stderr}\n"
        
        report_status = "Playwright test report generated. Use 'npx playwright show-report' to view it manually."
        output += f"\n{report_status}"
        return output
    
    except subprocess.TimeoutExpired:
        return "Test execution timed out after 20 seconds."
    except Exception as e:
        return f"Error executing test: {str(e)}"

def display_test_cases():
    """Display test cases from form_validation_test_cases1.csv in a table."""
    try:
        csv_path = "agent/form_validation_test_cases1.csv"
        if not os.path.exists(csv_path):
            st.session_state.chat_history.append(("System", "Error: form_validation_test_cases1.csv not found."))
            return
        
        df = pd.read_csv(csv_path)
        if df.empty:
            st.session_state.chat_history.append(("System", "No test cases found in the CSV."))
            return
        
        # st.subheader("Current Test Cases")
        # Display all columns for full visibility
        st.dataframe(df)
        # st.session_state.chat_history.append(("System", f"Displayed {len(df)} test cases."))
    except Exception as e:
        st.session_state.chat_history.append(("System", f"Error displaying test cases: {str(e)}"))


def remove_test_case_from_csv(test_case_id):
    """Remove a test case from form_validation_test_cases1.csv by Test_Case_ID."""
    try:
        csv_path = "agent/form_validation_test_cases1.csv"
        if not os.path.exists(csv_path):
            return False, "Error: form_validation_test_cases1.csv not found."

        df = pd.read_csv(csv_path)
        if test_case_id not in df["Test_Case_ID"].values:
            return False, f"Test case {test_case_id} not found."
        
        df = df[df["Test_Case_ID"] != test_case_id]
        df.to_csv(csv_path, index=False)
        return True, f"Test case {test_case_id} removed successfully."
    except Exception as e:
        return False, f"Error removing test case: {str(e)}"


# Function to reorder Test_Case_IDs
def reorder_test_case_ids(csv_path):
    """Reorder Test_Case_IDs in the CSV to be sequential (TC001, TC002, etc.)."""
    try:
        if not os.path.exists(csv_path):
            return False, f"Error: {csv_path} not found."
        
        df = pd.read_csv(csv_path)
        if df.empty:
            return True, "No test cases to reorder."
        
        # Generate new Test_Case_IDs
        df["Test_Case_ID"] = [f"TC{index+1:03d}" for index in range(len(df))]
        df.to_csv(csv_path, index=False)
        return True, "Test case IDs reordered successfully."
    except Exception as e:
        return False, f"Error reordering test case IDs: {str(e)}"


# Title
st.title("🤖 Form Validation Testing with Playwright")
# Instructions for input commands
st.markdown("""
**How to Use the Chat Input:**

Use the following formats to manage test cases:

```plaintext
# Remove a test case
remove test case <test_case_id>
Example: remove test case TC001,TC002 TC003

# Display all test cases
display test cases

# test the test_cases
test cases

# Generate test cases from Google Doc
generate test cases from <google_doc_url>
Example: generate test cases from https://docs.google.com/document/d/1FJGQMGvB_G42gaY5jDFZjMK41mx-65uXWbgTaNrG-hI/edit
```

Any other input will trigger the execution of all test cases in the CSV.
""")

# Input field
user_input = st.text_input("You:", key="user_input", label_visibility="visible")

# Send message button
if st.button("Send") and user_input:
    async def handle_input():
        try:
            
            generate_match = re.match(r"generate test cases from\s+(.+)", user_input, re.IGNORECASE)
            if generate_match:
                url = generate_match.group(1).strip()
                if not url:
                    st.session_state.chat_history.append(("System", "Error: No Google Doc URL provided."))
                    # st.session_state.chat_history.append(("System", "Ready for your next command."))
                    return
                try:
                    st.session_state.chat_history.append(("System", f"Extracting test cases from Google Doc: {url}"))
                    doc_text = testcase_extractor.get_text_from_google_doc(url)
                    if not doc_text:
                        st.session_state.chat_history.append(("System", "Error: Could not extract text from the Google Doc."))
                        st.session_state.chat_history.append(("System", "Ready for your next command."))
                        return
                    csv_path = "agent/form_validation_test_cases1.csv"
                    start_time = time.time()
                    with st.spinner("Generating and Saving test cases... Please wait ⏳"):
                        testcase_extractor.generate_test_cases_csv(doc_text, csv_path)
                    end_time = time.time()
                    duration = end_time - start_time
                    st.session_state.chat_history.append(("System", f"✅ Test cases generated and saved to {csv_path} in {duration:.2f} seconds."))
                    st.session_state.show_test_cases = True
                    st.session_state.chat_history.append(("System", "Ready for your next command."))
                    return
                except Exception as e:
                    st.session_state.chat_history.append(("System", f"Error generating test cases from Google Doc: {str(e)}"))
                    st.session_state.chat_history.append(("System", "Ready for your next command."))
                    return
            # Check for display test cases command
            display_match = re.match(r"display test cases", user_input, re.IGNORECASE)
            if display_match:
                st.session_state.chat_history.append(("System", "Displaying current test cases."))
                st.session_state.show_test_cases = True
                st.session_state.chat_history.append(("System", "Ready for your next command."))
                return

            # Check for remove test case command (updated to handle multiple IDs and reordering)
            remove_match = re.match(r"remove test case\s+([\w\s,]+)", user_input, re.IGNORECASE)
            if remove_match:
                # Split test case IDs by commas or spaces, remove whitespace
                test_case_ids = [tid.strip() for tid in re.split(r"[,\s]+", remove_match.group(1)) if tid.strip()]
                if not test_case_ids:
                    st.session_state.chat_history.append(("System", "No valid test case IDs provided."))
                    st.session_state.chat_history.append(("System", "Ready for your next command."))
                    return
                
                success_count = 0
                for test_case_id in test_case_ids:
                    success, message = remove_test_case_from_csv(test_case_id)
                    st.session_state.chat_history.append(("System", message))
                    if success:
                        success_count += 1
                
                # Reorder Test_Case_IDs if any removals succeeded
                if success_count > 0:
                    csv_path = "agent/form_validation_test_cases1.csv"
                    reorder_success, reorder_message = reorder_test_case_ids(csv_path)
                    st.session_state.chat_history.append(("System", reorder_message))
                    st.session_state.show_test_cases = True  # Refresh test case display
                
                st.session_state.chat_history.append(("System", f"Processed {len(test_case_ids)} test case removal(s), {success_count} succeeded."))
                st.session_state.chat_history.append(("System", "Ready for your next command."))
                return

            # # Check for remove test case command
            # remove_match = re.match(r"remove test case\s+(\w+)", user_input, re.IGNORECASE)
            # if remove_match:
            #     test_case_id = remove_match.group(1)
            #     success, message = remove_test_case_from_csv(test_case_id)
            #     st.session_state.chat_history.append(("System", message))
            #     if success:
            #         st.session_state.show_test_cases = True  # Refresh test case display
            #     st.session_state.chat_history.append(("System", "Ready for your next command."))
            #     return
            test_match = re.match(r"test cases", user_input, re.IGNORECASE)
            if test_match:
                # with st.spinner("Refining Test Steps... Please wait ⏳"):
                #     df = pd.read_csv("agent/form_validation_test_cases1.csv")
                #     filtered_df = filter_by_expected_result(df,no=2, type="Regression") # type can be "Success", "Failure", or "Regression" to get all rows
                #     testcase_extractor.logger.info(f"Processing {len(filtered_df)} rows...")
                #     filtered_df[output_column] = filtered_df.apply(process_row, axis=1)
                #     filtered_df.to_csv("agent/ai_ready_test_steps.csv", index=False)
                #     testcase_extractor.logger.info(f"✅ Test cases saved to {"agent/ai_ready_test_steps.csv"}")
                # Existing test execution logic
                prmpt = pd.read_csv("agent/ai_ready_test_steps.csv")
                prmpt = prmpt.iloc[2:4]
                all_test_results = []
                
                for index, row in prmpt.iterrows():
                    refine_prompt = row['Test_Steps_AI']
                    # st.session_state.chat_history.append(("System", f"Processing Test Case {index + 1}: {refine_prompt}"))
                    
                    try:
                        # main_prompt = build_system_prompt(refine_prompt)
                        response = await st.session_state.agent.run("\n\nUser Instructions:\n"+refine_prompt.strip())
                        st.session_state.chat_history.append(("You", f"Test Case {index + 1}: {refine_prompt}"))
                        st.session_state.chat_history.append(("Assistant", response))
                        
                        code = extract_typescript_code(response)
                        if code:
                            st.session_state.chat_history.append(("System", f"Extracting and running Playwright test code for Test Case {index + 1}..."))
                            test_results = save_and_run_ts_code(code, index + 1)
                            st.session_state.chat_history.append(("Test Results", f"Test Case {index + 1} Results:\n{test_results}"))
                            st.session_state.chat_history.append(("System", f"Test code for Test Case {index + 1} saved to test_report{index + 1}.spec.ts"))
                        else:
                            st.session_state.chat_history.append(("System", f"No TypeScript code found in the response for Test Case {index + 1}."))
                        
                        all_test_results.append({
                            "Test Case": f"Test Case {index + 1}",
                            "Steps": refine_prompt,
                            "Status": "Completed" if code else "No Code Generated",
                            "Results": test_results if code else "N/A"
                        })
                    
                    except Exception as e:
                        st.session_state.chat_history.append(("Error", f"Error processing Test Case {index + 1}: {str(e)}"))
                        all_test_results.append({
                            "Test Case": f"Test Case {index + 1}",
                            "Steps": refine_prompt,
                            "Status": "Failed",
                            "Results": f"Error: {str(e)}"
                        })
                
                st.session_state.chat_history.append(("System", "All test cases processed."))

            # Handle invalid input
            st.session_state.chat_history.append(("System", "Invalid input. Please enter a valid command."))
            st.session_state.chat_history.append(("System", "Ready for your next command."))
        except Exception as e:
            st.session_state.chat_history.append(("Error", f"Error reading CSV or processing test cases: {str(e)}"))
    
    asyncio.run(handle_input())
    st.rerun()
# Display test cases table if flag is set
if st.session_state.show_test_cases:
    display_test_cases()

# Display chat history
for role, message in st.session_state.chat_history:
    if role == "You":
        st.markdown(f"🧑 **{role}:** {message}")
    elif role == "Assistant":
        st.markdown(f"🤖 **{role}:** {message}")
    elif role == "Test Results":
        st.code(message)
    elif role == "System":
        st.info(message)
    else:
        st.error(f"{role}: {message}")

# Clear conversation history
if st.button("Clear History"):
    st.session_state.agent.clear_conversation_history()
    st.session_state.chat_history = []
    st.rerun()