// tests/form-success.spec.ts
import { test, expect, Page } from '@playwright/test';

test.describe('Form Submission Without Errors', () => {
  const FORM_URL = 'https://emlabsform.onrender.com/';
  const TIMEOUT_NAV = 10000;
  const TIMEOUT_FIELD = 5000;

  /**
   * Locate an input by its exact label text and assert visibility.
   * @param page Playwright Page
   * @param label Exact label text
   * @param missingMsg Error message if the field is not found
   */
  async function getField(page: Page, label: string, missingMsg: string) {
    const field = page.getByLabel(label);
    await expect(field, missingMsg).toBeVisible({ timeout: TIMEOUT_FIELD });
    return field;
  }

  /**
   * Click the Submit button and wait for network idle.
   */
  async function submitForm(page: Page) {
    const submit = page.getByRole('button', { name: 'Submit' });
    await expect(submit, 'Submit button missing')
      .toBeVisible({ timeout: TIMEOUT_FIELD });
    await submit.click();
    await page.waitForLoadState('networkidle', { timeout: TIMEOUT_NAV });
  }

  test('fills mandatory fields only and submits without error', async ({ page }) => {
    // 1. Navigate to page
    await page.goto(FORM_URL, { waitUntil: 'load', timeout: TIMEOUT_NAV })
      .catch(() => { throw new Error('Page failed to load'); });

    // 2. First Name (optional): leave empty
    await getField(page, 'First Name', 'First Name field missing');

    // 3. Last Name (optional): leave empty
    await getField(page, 'Last Name', 'Last Name field missing');

    // 4. Phone Number (mandatory)
    const phone = await getField(page, 'Phone Number', 'Phone Number field missing');
    await phone.fill('123456789', { timeout: TIMEOUT_FIELD });

    // 5. Email (mandatory)
    const email = await getField(page, 'Email', 'Email field missing');
    await email.fill('<EMAIL>', { timeout: TIMEOUT_FIELD });

    // 6. Website (optional): leave empty
    await getField(page, 'Website', 'Website field missing');

    // 7. Date of Birth (mandatory)
    const dob = await getField(page, 'Date of Birth', 'Date of Birth field missing');
    await dob.fill('14-May-2000', { timeout: TIMEOUT_FIELD });

    // 8. Submit
    await submitForm(page);

    // 9. Verify no visible "error" text
    await page.evaluate(() => document.body.scrollIntoView());
    const errorLocator = page.getByText(/error/i);
    const found = await errorLocator.isVisible({ timeout: TIMEOUT_NAV }).catch(() => false);
    if (found) {
      throw new Error('Error message detected after form submission');
    }
  });
});